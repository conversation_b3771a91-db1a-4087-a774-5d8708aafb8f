#include <bits/stdc++.h>
using namespace std;
class Solution {
  public:
    int countDays(int days, vector<vector<int>> &meetings) {
        // 按起点排序
        sort(meetings.begin(), meetings.end());
        int occupied = 0;
        int prev_start = -1, prev_end = -1;
        for (const auto &m : meetings) {
            int start = m[0], end = m[1];
            if (start > prev_end) {
                // 不重叠
                occupied += end - start + 1;
                prev_start = start;
                prev_end = end;
            } else {
                // 重叠，合并区间
                if (end > prev_end) {
                    occupied += end - prev_end;
                    prev_end = end;
                }
            }
        }
        return days - occupied;
    }
};