#include<iostream>
using namespace std;
class Solution {
public:
    double quickmul(double x,long long n)
    {
        if(n==0) return 1.0;
        double y=quickmul(x,n/2);
        return n&1?  x*y*y : y*y;

    }
    double myPow(double x, long long n) {
        return n>=0? quickmul(x,n) : quickmul(1.0/x,-n);

    }
};
int main()
{
    Solution sol;
    double x=1.2;
    int n=-3;
    double res=sol.myPow(x,n);
    cout<<res<<endl;
    return 0;
}