#include<queue>
#include<iostream>
#include<algorithm>
using namespace std;
int main()
{
    int k,m;
    cin>>k>>m;
    string s="";
    priority_queue<int, vector<int>, greater<int> >q;
    q.push(1);
    for(int i=0;i<k;i++)
    {
        int x=q.top();
        q.pop();
        s+=to_string(x);
        q.push(2*x+1);
        q.push(4*x+5);
    }
    int i;
    int cnt=0;
    cout<<s<<endl;
    for(int i=0;i<s.size()-1;i++)
    {
        if(s[i]<s[i+1])
        {
            s.erase(i,1);
            i--;
            cnt++;
            if(cnt==m) break;
        }
    }
    cout<<s<<endl;
    return 0;
}