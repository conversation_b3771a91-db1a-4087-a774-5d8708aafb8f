#include<queue>
#include<algorithm>
using namespace std;
class Solution {
public:
    int maxPerformance(int n, vector<int>& speed, vector<int>& efficiency, int k) {
        vector<vector<int>> v;
        priority_queue<int,vector<int>,greater<int>> pq;
        for(int i=0;i<n;i++)
        {
            v.push_back({efficiency[i],speed[i]});
        }
        sort(v.rbegin(),v.rend());
        long long ans=0,sum=0;
        int eff=0;
        for(int i=0;i<n;i++)
        {
            eff=v[i][0];
            sum+=v[i][1];
            pq.push(v[i][1]);
            if(--k<0)
            {
                sum-=pq.top();
                pq.pop();
            }
            ans=max(ans,sum*eff);
        }
        return ans%1000000007;


    }
};