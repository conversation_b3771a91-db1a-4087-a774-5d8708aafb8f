#include <iostream>
#include <fstream>
#include <windows.h>
#include <stdio.h>
using namespace std;
#define MAX 2000
int n;                  //物品数量
int c = 500;            //背包容量
double v[MAX];          //物品价值数组
double w[MAX];          //物品重量数组
int cw = 0;             //当前背包重量
int cp = 0;             //当前物品价值
int bestp = 0;          //最优价值
double perp[MAX];       //价值排序，价值/重量
double order[MAX];      //物品编号
int put[MAX] = {0};     //是否装入
int bestway[MAX] = {0}; //回溯最优路径

void swap(double *a, double *b)
{
    double temp = *a;
    *a = *b;
    *b = temp;
}


void knapsack()
{
    int i, j;
    for (i = 1; i <= n; i++)
        perp[i] = v[i] / w[i];
    for (i = 1; i <= n - 1; i++)
    {
        for (j = i + 1; j <= n; j++)
            if (perp[i] < perp[j]) 
            {
                swap(&perp[i], &perp[j]);
                swap(&order[i], &order[j]);
                swap(&v[i], &v[j]);
                swap(&w[i], &w[j]);
            }
    }
}


int bound(int i)
{
    int leftw = c - cw;
    int b = cp;
    while (i <= n && w[i] <= leftw)
    {
        leftw -= w[i];
        b += v[i];
        i++;
    }
    if (i <= n)
        b += v[i] / w[i] * leftw;
    return b;
}


void backtrack(int i)
{
    if (i > n)
    {
        bestp = cp;
        for (int i = 1; i <= n; i++)
            bestway[i] = put[i];
        return;
    }
    if (cw + w[i] <= c)
    {
        cw += w[i];
        cp += v[i];
        put[i] = 1;
        backtrack(i + 1);
        cw -= w[i];
        cp -= v[i];
        put[i] = 0;
    }
    if (bound(i + 1) > bestp) 
        backtrack(i + 1);
}

int main()
{
    int i;
    ifstream infile("01beibao_in.txt", ios::in);
    ofstream outfile("01beibao_out.txt", ios::out);
    infile >> n;
    for (i = 1; i <= n; i++)
    {
        infile >> w[i] >> v[i];
        order[i] = i;
    }
    infile.close();
    double time = 0;
    LARGE_INTEGER nFreq, nBeginTime, nEndTime;
    QueryPerformanceFrequency(&nFreq);
    QueryPerformanceCounter(&nBeginTime); 
    knapsack();
    backtrack(1);
    QueryPerformanceCounter(&nEndTime);                                                
    time = (double)(nEndTime.QuadPart - nBeginTime.QuadPart) / (double)nFreq.QuadPart; 
    outfile << "最大价值：" << bestp << "\n";
    outfile << "需要装入：\n";
    for (i = 1; i <= n; i++)
    {
        if (bestway[i] == 1)
            outfile << order[i] << " " << w[i] << " " << v[i] << endl;
    }
    outfile << "耗时" << time * 1000 << "ms" << endl;
    outfile.close();
    return 0;
}