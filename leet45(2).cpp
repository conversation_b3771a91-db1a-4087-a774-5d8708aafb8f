#include<bits/stdc++.h>
using namespace std;
class Solution {
public:
    int jump(vector<int>& nums) {
        int end=0;
        int ans=0;
        int maxpos=0;
        for(int i=0;i<nums.size()-1;i++)
        {
            maxpos=max(maxpos,nums[i]+i);
            if(i==end)
            {
                end=maxpos;
                ans++;
            }
        }
        return ans;

    }
};
int main()
{
    vector<int> v{2,3,0,1,4};
    Solution sol;
    int res=sol.jump(v);
    cout<<res<<endl;
    return 0;
}