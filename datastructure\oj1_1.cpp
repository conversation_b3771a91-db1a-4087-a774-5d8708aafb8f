#include<stack>
#include<unordered_map>
#include<iostream>
using namespace std;
bool isvalidop(char op)
{
    return op=='+' || op=='-' || op=='*' || op=='/';
}
void evaluate(stack<int> &values,stack<char> &ops)
{
    char op=ops.top();
    ops.pop();
    int right=values.top();
    values.pop();
    int left=values.top();
    values.pop();
    int result;
    switch(op)
    {
        case '+':
            result=left+right;
            break;
        case '-':
            result=left-right;
            break;
        case '*':
            result=left*right;
            break;
        case '/':
            result=left/right;
            break;
        default:
            return;
        
    }
    values.push(result);
}
int main()
{
    stack<int> values;
    stack<char> ops;
    unordered_map<char,int> priority={{'+',1},{'-',1},{'*',2},{'/',2}};
    string s;
    cin>>s;
    for(int i=0;i<s.length()-1;i++)
    {
        if(isdigit(s[i]))
        {
            int value=0;
            while(isdigit(s[i]) && i<s.length())
            {
                value=value*10+(s[i]-'0');
                i++;
            }
            values.push(value);
            i--;
            
        }
        else if(s[i]=='(')
        {
            ops.push(s[i]);
        }
        else if(s[i]==')')
        {
            while(ops.top()!='(')
            {
                evaluate(values,ops);
            }
            ops.pop();
        }
        else if(isvalidop(s[i]))
        {
            while(!ops.empty() && priority[s[i]]<=priority[ops.top()]&&ops.top()!='(' )
            {
                evaluate(values,ops);
            }
            ops.push(s[i]);
        }
        else
        {
            cout<<"NO"<<endl;
            return 0;
        }
    }
    while(!ops.empty()) evaluate(values,ops);
    cout<<values.top()<<endl;
    return 0;

}