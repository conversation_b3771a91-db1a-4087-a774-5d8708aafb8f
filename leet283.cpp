#include <vector>
using namespace std;
class Solution
{
public:
    void moveZeroes(vector<int> &nums)
    {
        int cnt = 0;
        int size = nums.size();
        int nonzeroindex = 0;
        for (int i = 0; i < size; i++)
        {
            if (nums[i] != 0)
            {
                nums[nonzeroindex++] = nums[i];
            }
        }
        for (int i = nonzeroindex; i < size; i++)
        {
            nums[i] = 0;
        }
    }
};