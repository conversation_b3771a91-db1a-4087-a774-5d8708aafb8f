#include<vector>
#include<iostream>
using namespace std;
int fun(vector<int> &nums)
{
    int n=nums.size();
    vector<int> dp(n);
    int maxg=0;
    for(int i=1;i<n;i++)
    {
        for(int j=i-1;j>=0;j--)
        {
            if(nums[i]>=nums[j] && dp[i]<=dp[j])
            {
                dp[i]=dp[j]+1;
            }
            
        }
    }
    for(int i=0;i<n;i++)
    {
        maxg=max(maxg,dp[i]);
    }
    return maxg+1;
}
int main()
{
    int n;
    cin>>n;
    while(n--)
    {
        int t;
        cin>>t;
        vector<int> v(t);
        for(int i=0;i<t;i++)
        {
            cin>>v[i];
        }
        int res=fun(v);
        cout<<res<<endl;
        v.clear();
    }
    return 0;
}