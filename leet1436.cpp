#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    string destCity(vector<vector<string>> &paths)
    {
        unordered_map<string, string> start_to_end;
        string &begin = paths[0][0];

        for (const auto &path : paths)
        {
            start_to_end[path[0]] = path[1];
        }
        while (start_to_end.count(begin))
        {
            begin = start_to_end[begin];
        }

        return begin;
    }
};