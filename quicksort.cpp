#include <vector>
#include <random>

// 1. 固定选择中间元素作为pivot
int choosePivotMiddle(std::vector<int> &arr, int left, int right)
{
    return arr[(left + right) / 2];
}

// 2. 随机选择pivot
int choosePivotRandom(std::vector<int> &arr, int left, int right)
{
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(left, right);
    return arr[dis(gen)];
}

// 3. 三数取中法选择pivot
int choosePivotMedianOfThree(std::vector<int> &arr, int left, int right)
{
    int mid = (left + right) / 2;
    if (arr[left] > arr[mid])
        std::swap(arr[left], arr[mid]);
    if (arr[left] > arr[right])
        std::swap(arr[left], arr[right]);
    if (arr[mid] > arr[right])
        std::swap(arr[mid], arr[right]);
    return arr[mid];
}

void quickSort(std::vector<int> &arr, int left, int right, int pivotStrategy = 1)
{
    if (left >= right)
        return;

    int pivot;
    switch (pivotStrategy)
    {
    case 1:
        pivot = choosePivotMiddle(arr, left, right);
        break;
    case 2:
        pivot = choosePivotRandom(arr, left, right);
        break;
    case 3:
        pivot = choosePivotMedianOfThree(arr, left, right);
        break;
    default:
        pivot = choosePivotMiddle(arr, left, right);
    }

    int i = left, j = right;
    while (i <= j)
    {
        while (arr[i] < pivot)
            i++;
        while (arr[j] > pivot)
            j--;

        if (i <= j)
        {
            std::swap(arr[i], arr[j]);
            i++;
            j--;
        }
    }

    quickSort(arr, left, j, pivotStrategy);
    quickSort(arr, i, right, pivotStrategy);
}

// 测试不同pivot选择策略
#include <iostream>
#include <chrono>

void testQuickSort(std::vector<int> arr, int strategy)
{
    auto start = std::chrono::high_resolution_clock::now();
    quickSort(arr, 0, arr.size() - 1, strategy);
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);

    std::cout << "策略 " << strategy << " 耗时: " << duration.count() << " 微秒" << std::endl;
}

int main()
{
    std::vector<int> arr = {64, 34, 25, 12, 22, 11, 90};
    std::vector<int> arr1 = arr;
    std::vector<int> arr2 = arr;
    std::vector<int> arr3 = arr;

    std::cout << "测试不同的pivot选择策略：" << std::endl;
    testQuickSort(arr1, 1); // 中间元素
    testQuickSort(arr2, 2); // 随机选择
    testQuickSort(arr3, 3); // 三数取中

    return 0;
}