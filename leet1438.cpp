#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    int longestSubarray(vector<int> &nums, int limit)
    {
        int i = 0;
        map<int, int> m;
        int ans = 0;
        for (int j = 0; j < nums.size(); j++)
        {
            m[nums[j]]++;
            while (m.rbegin()->first - m.begin()->first > limit)
            {
                m[nums[i]]--;
                if (m[nums[i]] == 0)
                {
                    m.erase(nums[i]);
                }
                i++;
            }
            ans = max(ans, j - i + 1);
        }
        return ans;
    }
};