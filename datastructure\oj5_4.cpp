#include <iostream>
#include <vector>
using namespace std;
int main()
{
    ios::sync_with_stdio(false);
    cin.tie(nullptr);
    int n;
    vector<int> ans;
    cin >> n;
    vector<int> p(n + 1);
    vector<int> a(n + 1);
    vector<vector<int>> adj(n + 1);
    for (int i = 1; i <= n; i++)
    {
        cin >> p[i] >> a[i];
        if (p[i] != -1)
        {
            adj[p[i]].push_back(i);
        }
    }
    for (int i = 1; i <= n; i++)
    {
        if (a[i] == 1)
        {
            int k = 1;
            for (int j = 0; j < adj[i].size(); j++)
            {
                k &= a[adj[i][j]];
            }
            if (k == 1)
            {
                ans.push_back(i);
                for (int j = 0; j < adj[i].size(); j++)
                {
                    p[adj[i][j]] = p[i];
                }
            }
        }
    }
    int size = ans.size();
    if (size == 0)
    {
        cout << -1;
    }
    else
    {
        for (int i = 0; i < size; i++)
        {
            cout << ans[i] << " ";
        }
    }
    return 0;
}