#include<iostream>
using namespace std;
long long m,n,x,k;
long long quickmulti(long long x,long long n,int m)
{
    if(n==0) return 1%m;
    if(n==1) return x%m;
    int y=quickmulti(x,n/2,m);
    return n&1? (y*y*x)%m : (y*y)%m;
}
int main()
{
    ios::sync_with_stdio(false);
    cin.tie(0);
    cout.tie(0);
    cin>>m;
    cin>>n;
    long long sum=0;
    for(int i=0;i<n;i++)
    {
        cin>>x>>k;
        sum+=quickmulti(x%m,k,m);
        sum=sum%m;
    }
    cout<<sum<<endl;
    return 0;
}