#include <queue>
#include <iostream>
#include <unordered_set>
using namespace std;
class node
{
public:
    int x;
    int y;
    int cost;
};
struct compare
{
    bool operator()(node a, node b) { return a.cost > b.cost; }
};
class unionset
{
public:
    int n;
    vector<int> f;
    unionset(int _n) : n(_n), f(n + 1)
    {
        for (int i = 1; i <= n; i++)
        {
            f[i] = i;
        }
    }
    int find(int x)
    {
        return x == f[x] ? x : f[x] = find(f[x]);
    }
    void merge(int x, int y)
    {
        int fx = f[x];
        int fy = f[y];
        if (fx == fy)
            return;
        f[fx] = fy;
    }
};
int main()
{
    priority_queue<node, vector<node>, compare> q;
    int n, m;
    int ans = 0;
    cin >> n >> m;
    unionset u(n);
    for (int i = 0; i < m; i++)
    {
        int x, y, m;
        cin >> x >> y >> m;
        q.push({x, y, m});
    }
    while (!q.empty())
    {
        node t = q.top();
        q.pop();
        if (u.find(t.x) != u.find(t.y))
        {
            u.merge(t.x, t.y);
            ans += t.cost;
        }
        else
        {
            continue;
        }
    }
    cout << ans << endl;
    return 0;
}