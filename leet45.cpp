#include<iostream>
#include<vector>
using namespace std;
class Solution {
public:
    int jump(vector<int>& nums) {
        int size=nums.size();
        vector<int> dp(size,100001);
        for(int i=0;i<size-1;i++)
        {
            for(int j=1;j<=nums[i];j++)
            {
                if(i+j<size)
                {
                    dp[i+j]=min(dp[i]+1,dp[i+j]);
                }
                else
                {
                    break;
                }
            }
        }
        return dp[size-1];

    }
};
int main()
{
    vector<int> v{2,3,0,1,4};
    Solution sol;
    int res= sol.jump(v);
    cout<<res<<endl;
    return 0;
}
