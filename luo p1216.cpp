#include<bits/stdc++.h>
using namespace std;
int main()
{
    int n;
    cin>>n;
    int a[n+1][n+1];
    int ans=0;
    for(int i=1;i<=n;i++)
    {
        for(int j=1;j<=i;j++)
        {
            cin>>a[i][j];
        }
    }
    int dp[n+1][n+1];
    dp[1][1]=a[1][1];
    for(int i=2;i<=n;i++)
    {
        dp[i][1]=a[i][1]+dp[i-1][1];
        dp[i][i]=a[i][i]+dp[i-1][i-1];
    }
    for(int i=3;i<=n;i++)
    {
        for(int j=2;j<i;j++)
        {
            dp[i][j]=max(dp[i-1][j-1]+a[i][j],dp[i-1][j]+a[i][j]);
        }
    }
    for(int j=1;j<=n;j++)
    {
        if(dp[n][j]>ans) ans=dp[n][j];
    }
    cout<<ans;
    return 0;
}