#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    int countGoodNodes(vector<vector<int>> &edges)
    {
        int n = edges.size() + 1;
        vector<vector<int>> g(n);
        for (auto &e : edges)
        {
            g[e[0]].push_back(e[1]);
            g[e[1]].push_back(e[0]);
        }
        int ans = 0;
        auto dfs = [&](auto &&dfs, int x, int p) -> int
        {
            int size = 1;
            int size0 = 0;
            bool good = true;
            for (auto &y : g[x])
            {
                if (y == p)
                {
                    continue;
                }
                int sz = dfs(dfs, y, x);
                if (size0 == 0)
                {
                    size0 = sz;
                }
                else if (size0 != sz)
                {
                    good = false;
                }
                size += sz;
            }
            if (good)
            {
                ans++;
            }
            return size;
        };
        dfs(dfs, 0, -1);
        return ans;
    }
};
int main()
{
    Solution s;
    vector<vector<int>> edges = {{0, 1}, {0, 2}, {1, 3}, {1, 4}, {2, 5}, {2, 6}};
    cout << s.countGoodNodes(edges) << endl;
    return 0;
}