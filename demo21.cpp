#include <chrono>
#include <functional> // for std::ref
#include <iostream>
#include <latch> // 需要 C++20
#include <string>
#include <thread> // 使用 std::jthread (C++20) 或 std::thread
#include <vector>

// 工作线程的初始化函数
void worker_initialization(int id, std::latch &init_latch) {
    std::cout << "[工作线程 " << id << "] 开始初始化..." << std::endl;
    // 模拟初始化工作，耗时不同
    std::this_thread::sleep_for(std::chrono::milliseconds(id * 150 + 100));
    std::cout << "[工作线程 " << id << "] 初始化完成。" << std::endl;

    // 完成初始化后，减少 latch 的计数
    init_latch.count_down();
    // count_down() 之后，线程可以继续执行其他任务，或者直接结束
}

int main() {
    const int num_workers = 4;
    std::cout << "[主线程] 程序开始。" << std::endl;

    // 创建一个 latch，初始计数为 num_workers
    // 当计数减到 0 时，所有在 wait() 上等待的线程将被唤醒
    std::latch initialization_complete(num_workers);

    std::vector<std::jthread> workers; // 使用 std::jthread (C++20) 自动管理线程生命周期 (自动 join)
    std::cout << "[主线程] 启动 " << num_workers << " 个工作线程进行初始化..." << std::endl;

    for (int i = 0; i < num_workers; ++i) {
        // 创建并启动线程，传递 latch 的引用
        workers.emplace_back(worker_initialization, i, std::ref(initialization_complete));
    }

    std::cout << "[主线程] 等待所有工作线程完成初始化..." << std::endl;
    // 主线程在此等待，直到 latch 的计数变为 0
    initialization_complete.wait();

    std::cout << "[主线程] 所有工作线程已完成初始化，主线程继续执行后续任务..." << std::endl;
    // ... 执行依赖于初始化的后续操作 ...

    std::cout << "[主线程] 程序结束。" << std::endl;
    // std::jthread 会在 workers 析构时自动调用 join()，无需手动 join

    return 0;
}

/*
编译命令 (需要 C++20 支持):
g++ your_code_name.cpp -o latch_sync_app -std=c++20 -pthread
或者
clang++ your_code_name.cpp -o latch_sync_app -std=c++20 -pthread
(确保你的编译器 g++/clang++ 版本支持 C++20)
*/
// ``` **体现的概念 ** : ***一次性屏障(One - off Barrier) * *
//     : `std::latch` 作为一个同步点，确保一组操作（这里是初始化）全部完成后，等待线程（主线程）才能继续执行。
//     ***计数同步
//         ** : 通过 `count_down()` 减少计数，`wait()` 等待计数归零来实现同步。 * `std::jthread` (C++ 20)
//     : 简化线程管理，在其析构时自动 `join`，避免忘记 `join` 导致的资源泄露或程序异常