#include <bits/stdc++.h>
using namespace std;
class Solution {
  public:
    int duplicateNumbersXOR(vector<int> &nums) {
        unordered_set<int> s;
        int ans = 0;
        for (int i = 0; i < nums.size(); i++) {
            if (s.count(nums[i]) == 0) {
                s.insert(nums[i]);
            } else {
                ans = ans ^ nums[i];
            }
        }
        return ans;
    }
};