#include<iostream>
#include<vector>
using namespace std;
class Solution {
public:
    vector<vector<string>> solveNQueens(int n) {
        len=n;
        int x[n];
        for(int i=0;i<n;i++)
        {
            x[i]=i+1;
        }
        dfs(0,x);
        return res;

    }
private:
    int len;
    vector<vector<string>> res;
    bool place(int k,int x[])
    {
        for(int i=0;i<k;i++)
        {
            if(abs(i-k)==abs(x[i]-x[k])) return false;
        }
        return true;
    }

    void dfs(int t,int x[])
    {
        if(t==len)
        {
            vector<string>temp;
            string s(len,'.');
            for(int i=0;i<len;i++)
            {
                string temp_s=s;
                temp_s[x[i]-1]='Q';
                temp.push_back(temp_s);
            }
            res.push_back(temp);
            return;
        }
        for(int i=t;i<len;i++)
        {
            swap(x[i],x[t]);
            if(place(t,x))
            {
                dfs(t+1,x);
            }
            swap(x[i],x[t]);
        }
    }
    
};
int main()
{
    Solution sol;
    auto res=sol.solveNQueens(4);
    return 0;
}