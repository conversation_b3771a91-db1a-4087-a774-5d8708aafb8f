#include <fstream>
#include <iostream>
#include <mutex>
#include <thread>
#include <unordered_map>
#include <vector>
#include <algorithm>
#include <sstream>

using namespace std;

struct TrieNode {
    unordered_map<char, TrieNode*> children;
    int count = 0;
    bool isEndOfWord = false;

    ~TrieNode() {
        for (auto &pair : children) {
            delete pair.second;
        }
    }
};

mutex trieMutex;

void insert(TrieNode* root, const string &word) {
    TrieNode* node = root;
    for (char c : word) {
        if (node->children.find(c) == node->children.end()) {
            node->children[c] = new TrieNode();
        }
        node = node->children[c];
    }
    node->isEndOfWord = true;
    node->count++;
}

void collectWords(TrieNode* node, const string &word, vector<pair<string, int>> &words) {
    if (node->isEndOfWord) {
        words.push_back(make_pair(word, node->count));
    }
    for (auto &p : node->children) {
        collectWords(p.second, word + p.first, words);
    }
}

void processLines(vector<string> &lines, TrieNode* root) {
    for (string &line : lines) {
        istringstream stream(line);
        string word;
        while (stream >> word) {
            string cleanedWord;
            for (char c : word) {
                if (isalpha(c)) {
                    cleanedWord += tolower(c);
                }
                 else
                {
                    if(!cleanedWord.empty())
                    {
                      insert(root, cleanedWord);
                      cleanedWord.clear();
                    }
               }
            }
            if (!cleanedWord.empty()) {
                lock_guard<mutex> lock(trieMutex);
                insert(root, cleanedWord);
            }
        }
    }
}

int main() {
    const int numThreads = 4;
    ifstream file("in.txt");
    string line;
    vector<thread> threads;
    TrieNode* root = new TrieNode();
    vector<vector<string>> linesForThreads(numThreads);

    if (!file.is_open()) {
        cerr << "Unable to open file in.txt" << endl;
        return 1;
    }

    int lineCount = 0;
    while (getline(file, line)) {
        int threadIndex = lineCount % numThreads;
        linesForThreads[threadIndex].push_back(line);
        ++lineCount;
    }

    for (int i = 0; i < numThreads; ++i) {
        threads.push_back(thread(processLines, ref(linesForThreads[i]), root));
    }

    for (thread &t : threads) {
        if (t.joinable()) {
            t.join();
        }
    }

    file.close();

    vector<pair<string, int>> words;
    collectWords(root, "", words);

    sort(words.begin(), words.end(), [](const pair<string, int>& a, const pair<string, int>& b) {
        if(a.second == b.second)
        {
          return a.first<b.first;
        }
        return a.second > b.second; 
    });
    int i=0;
    for (const auto &pair : words) {
        i++;
        cout << pair.first << " " << pair.second << endl;
        if(i==100) break;
    }

    delete root;

    return 0;
}