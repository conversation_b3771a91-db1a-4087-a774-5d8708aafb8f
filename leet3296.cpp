#include <vector>
#include <algorithm>
using namespace std;
class Solution
{
public:
    // Helper function to calculate time needed for one worker to reduce height by x levels
    long long calculateTime(int baseTime, int levels)
    {
        long long time = 0;
        for (int i = 1; i <= levels; i++)
        {
            time += (long long)baseTime * i;
        }
        return time;
    }

    // Binary search to check if it's possible to reduce mountain to 0 within maxTime
    bool canReduceMountain(int mountainHeight, vector<int> &workerTimes, long long maxTime)
    {
        int totalReduction = 0;

        for (int workerTime : workerTimes)
        {
            // For each worker, find maximum levels they can reduce within maxTime
            int left = 0, right = mountainHeight;
            while (left < right)
            {
                int mid = left + (right - left + 1) / 2;
                if (calculateTime(workerTime, mid) <= maxTime)
                {
                    left = mid;
                }
                else
                {
                    right = mid - 1;
                }
            }
            totalReduction += left;
            if (totalReduction >= mountainHeight)
                return true;
        }

        return false;
    }

    long long minimumTime(int mountainHeight, vector<int> &workerTimes)
    {
        // Binary search for the minimum time needed
        long long left = 0;
        // Maximum possible time: longest worker time * height * (height + 1) / 2
        long long right = (long long)*max_element(workerTimes.begin(), workerTimes.end()) * mountainHeight * (mountainHeight + 1) / 2;

        while (left < right)
        {
            long long mid = left + (right - left) / 2;
            if (canReduceMountain(mountainHeight, workerTimes, mid))
            {
                right = mid;
            }
            else
            {
                left = mid + 1;
            }
        }

        return left;
    }
};