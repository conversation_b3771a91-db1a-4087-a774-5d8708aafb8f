#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    vector<string> generateParenthesis(int n)
    {
        vector<string> result;
        generateParenthesisHelper(result, "", n, n);
        return result;
    }

private:
    void generateParenthesisHelper(vector<string> &result, string current, int open, int close)
    {
        if (open == 0 && close == 0)
        {
            result.push_back(current);
            return;
        }
        if (open > 0)
        {
            generateParenthesisHelper(result, current + "(", open - 1, close);
        }

        if (close > open)
        {
            generateParenthesisHelper(result, current + ")", open, close - 1);
        }
    }
};
int main()
{
    Solution sol;
    vector<string> v = sol.generateParenthesis(3);

    for (auto it = v.begin(); it != v.end(); it++)
    {
        cout << *it << endl;
    }
    return 0;
}