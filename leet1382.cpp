#include<vector>
using namespace std;

  struct TreeNode {
      int val;
      TreeNode *left;
      TreeNode *right;
      TreeNode() : val(0), left(nullptr), right(nullptr) {}
      TreeNode(int x) : val(x), left(nullptr), right(nullptr) {}
      TreeNode(int x, TreeNode *left, TreeNode *right) : val(x), left(left), right(right) {}
  };
 
class Solution {
public:
    vector<int> v;
    TreeNode* balanceBST(TreeNode* root) {
        inorder(root);
        return build(0,v.size()-1);

    }
    void inorder(TreeNode* root)
    {
        if(root==nullptr) return;
        inorder(root->left);
        v.push_back(root->val);
        inorder(root->right);
    }
    TreeNode* build(int l,int r)
    {
        if(l>r) return nullptr;
        int mid=l+(r-l)/2;
        TreeNode* root=new TreeNode(v[mid]);
        root->left=build(l,mid-1);
        root->right=build(mid+1,r);
        return root;
    }

};