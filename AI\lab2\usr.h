#include<iostream>
#include<thread>
using namespace std;
int a[9];
int b[9]={0};
int c[16]={0};
int d[16]={0};
int sum=0;

void searchh(int i)
{
    for(int j=1;j<=8;j++)
    {
        if((!b[j])&&(!c[i+j])&&(!d[i-j+7]))//每个皇后都有八个位置(列)可以试放
        {
            /********** Begin **********/
            if(i==8)
            {
                sum++;//找到解
                return;
            }
            b[j]=c[i+j]=d[i-j+7]=1;//标记
            searchh(i+1);//搜索下一层
            b[j]=c[i+j]=d[i-j+7]=0;//回溯
            /********** End **********/
        }
    }
}