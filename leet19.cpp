struct ListNode {
     int val;
     ListNode *next;
     ListNode() : val(0), next(nullptr) {}
     ListNode(int x) : val(x), next(nullptr) {}
     ListNode(int x, ListNode *next) : val(x), next(next) {}
 };
class Solution {
public:
    ListNode* removeNthFromEnd(ListNode* head, int n) {
        ListNode *dummy=new ListNode(0,head);
        ListNode *p=dummy;
        ListNode *q=dummy;

        for(int i=0;i<n+1;i++)
        {
            q=q->next;
        }
        while(q)
        {
            q=q->next;
            p=p->next;
        }
        ListNode* deletenode=p->next;
        p->next=p->next->next;
        delete deletenode;
        ListNode *res=dummy->next;
        delete dummy;
        return res;





    }
};