#include <bits/stdc++.h>
using namespace std;
struct TreeNode {
    int val;
    TreeNode *left;
    TreeNode *right;
    TreeNode() : val(0), left(nullptr), right(nullptr) {}
    TreeNode(int x) : val(x), left(nullptr), right(nullptr) {}
    TreeNode(int x, TreeNode *left, TreeNode *right)
        : val(x), left(left), right(right) {}
};

class Solution {
  public:
    TreeNode *constructFromPrePost(vector<int> &preorder,
                                   vector<int> &postorder) {
        if (preorder.size() == 0)
            return NULL;
        if (preorder.size() == 1) {
            return new TreeNode(preorder[0]);
        }
        TreeNode *root = new TreeNode(preorder[0]);
        int i = find(postorder.begin(), postorder.end(), preorder[1]) -
                postorder.begin();
        vector<int> preleft(preorder.begin() + 1, preorder.begin() + i + 2);
        vector<int> preright(preorder.begin() + i + 2, preorder.end());
        vector<int> postleft(postorder.begin(), postorder.begin() + i + 1);
        vector<int> postright(postorder.begin() + i + 1, postorder.end() - 1);
        root->left = constructFromPrePost(preleft, postleft);
        root->right = constructFromPrePost(preright, postright);
        return root;
    }
};