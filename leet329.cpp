#include <vector>
using namespace std;
class Solution
{
public:
    int longestIncreasingPath(vector<vector<int>> &matrix)
    {
        if (matrix.empty() || matrix[0].empty()) return 0;
        m = matrix.size();
        n = matrix[0].size();
        int res=0;
        store=vector<vector<int>>(m, vector<int>(n, -1));
        
    }
    
private:
    int m;
    int n;
    vector<vector<int>> store;
    int dfs(vector<vector<int>> &matrix, vector<vector<int>> &store, int i, int j)
    {
        if()
        
    }
};