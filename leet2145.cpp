#include <bits/stdc++.h>
using namespace std;
class Solution {
  public:
    int numberOfArrays(vector<int> &differences, int lower, int upper) {
        int n = differences.size();
        long long minPrefix = 0, maxPrefix = 0, currentPrefix = 0;
        for (int i = 0; i < n; ++i) {
            currentPrefix += differences[i];
            minPrefix = min(minPrefix, currentPrefix);
            maxPrefix = max(maxPrefix, currentPrefix);
        }
        return max(0LL, (upper - lower) - (maxPrefix - minPrefix) + 1);
    }
};