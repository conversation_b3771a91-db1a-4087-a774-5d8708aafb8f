#include<string>
#include<vector>
#include<unordered_map>
#include<iostream>
using namespace std;
class Solution {
public:
    vector<string> findRepeatedDnaSequences(string s) {
        unordered_map <string,int> map;
        vector <string> ans;
        for(int i=0;i+9<=s.size()-1;i++)
        {
            string temp=s.substr(i,10);
            if(++map[temp]==2)
            {
                ans.push_back(temp);
            }
        }
        return ans;
        

    }
};
int main()
{
    Solution sol;
    auto res=sol.findRepeatedDnaSequences("AAAAAAAAAAAAA");
    for(auto &i: res)
    {
        cout<<i<<endl;
    }
    return 0;
}