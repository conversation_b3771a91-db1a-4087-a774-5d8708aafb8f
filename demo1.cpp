#include <iostream>
#include <vector>
#include <algorithm>
#include <memory>
using namespace std;
// 定义一个仿函数类
class MultiplyBy
{
public:
    explicit MultiplyBy(int factor) : factor_(factor) {}

    operator int() const
    {
        return 3 * factor_;
    }
    int operator()(int x) const
    {
        return x * factor_;
    }

private:
    int factor_;
};

int main()
{
    std::vector<int> numbers = {1, 2, 3, 4, 5};
    int factor = 3;
    unique_ptr<int> p(new int(3));
    // 使用仿函数进行元素变换
    MultiplyBy mul = MultiplyBy(factor);
    for (int i = 0; i < numbers.size(); i++)
    {
        cout << mul << " ";
    }
    cout << mul << endl;
    int x = MultiplyBy(3);

    return 0;
}
