#include<vector>
#include<algorithm>
#include<iostream>
using namespace std;
class point
{
public:
    int x;
    int y;
    point(int x,int y)
    {
       this->x=x;
       this->y=y;
    }
};
point p0(0,0);
int corssproduct(point p0,point p1,point p2)
{
    int x1=p1.x-p0.x;
    int y1=p1.y-p0.y;
    int x2=p2.x-p0.x;
    int y2=p2.y-p0.y;
    return x1*y2-x2*y1;
}
bool compare(point a,point b)
{
    int order=corssproduct(p0,a,b);
    if(order==0)
    {
        int x1=a.x-p0.x;
        int y1=a.y-p0.y;
        int x2=b.x-p0.x;
        int y2=b.y-p0.y;
        return x1*x1+y1*y1>x2*x2+y2*y2;
    }
    return order>0;
}
bool compare2(point a,point b)
{
    if(a.x==b.x)
    {
        return a.y<b.y;

    }
    else
    {
        return a.x<b.x;
    }
}
vector<point> scan(vector<point>& p)
{
    if(p.size()<=3) return p;
    int miny=p[0].y,min=0;
    for(int i=1;i<p.size();i++)
    {
        if(p[i].y<miny || p[i].y==miny&&p[i].x<p[min].x)
        {
            min=i;
            miny=p[i].y;
        }
    }
    swap(p[0],p[min]);
    p0=p[0];
    sort(p.begin()+1,p.end(),compare);
    for(int i=1;i<p.size()-1;i++)
    {
        while(corssproduct(p0,p[i],p[i+1])==0)
        {
            p.erase(p.begin()+i+1);
        }
    }
    vector<point> res;
    res.push_back(p[0]);
    res.push_back(p[1]);
    res.push_back(p[2]);
    for(int i=3;i<p.size();i++)
    {
        while(corssproduct(res[res.size()-2],res[res.size()-1],p[i])<=0)
        {
            res.pop_back();
        }
        res.push_back(p[i]);
    }
    sort(begin(res),res.end(),compare2);
    return res;
    
}
int main()
{
    int n;
    cin>>n;
    int x,y;
    vector<point> p;
    while(n--)
    {
        cin>>x>>y;
        p.emplace_back(x,y);
    }
    auto res=scan(p);
    for(int i=0;i<res.size();i++)
    {
        cout<<res[i].x<<" "<<res[i].y<<endl;
    }
    return 0;
}