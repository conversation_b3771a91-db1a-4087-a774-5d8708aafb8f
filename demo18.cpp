#include <atomic> // for std::atomic
#include <chrono>
#include <iostream>
#include <mutex>   // for std::mutex comparison
#include <numeric> // for std::iota
#include <thread>
#include <vector>

// 全局原子计数器
std::atomic<long long> atomic_counter(0);

// 对比：使用互斥锁保护的全局计数器
long long mutex_counter = 0;
std::mutex counter_mutex;

// 使用原子操作递增计数器的函数
void increment_atomic(int iterations) {
    for (int i = 0; i < iterations; ++i) {
        // 原子地增加计数器值
        // fetch_add 提供原子性的读-修改-写操作
        // std::memory_order_relaxed 是最宽松的内存序，对于简单计数器通常足够，可能性能最好
        // 也可以直接使用 atomic_counter++; 这通常等价于 fetch_add(1, std::memory_order_seq_cst)
        atomic_counter.fetch_add(1, std::memory_order_relaxed);
    }
}

// 使用互斥锁保护递增计数器的函数
void increment_mutex(int iterations) {
    for (int i = 0; i < iterations; ++i) {
        std::lock_guard<std::mutex> lock(counter_mutex); // 获取锁
        mutex_counter++;
    } // lock_guard 在析构时自动释放锁
}

int main() {
    const int num_threads = 8;                 // 可以调整线程数
    const int iterations_per_thread = 1000000; // 增加迭代次数以观察效果

    std::vector<std::thread> threads;
    long long expected_value = static_cast<long long>(num_threads) * iterations_per_thread;

    // --- 测试原子计数器 ---
    atomic_counter.store(0); // 重置计数器
    std::cout << "测试 std::atomic 计数器..." << std::endl;
    auto start_atomic = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back(increment_atomic, iterations_per_thread);
    }
    for (auto &t : threads) {
        t.join();
    }
    auto end_atomic = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> duration_atomic = end_atomic - start_atomic;
    threads.clear(); // 清空线程向量

    std::cout << "原子计数器最终值: " << atomic_counter.load() << std::endl;
    std::cout << "原子计数器耗时: " << duration_atomic.count() << " ms" << std::endl;
    if (atomic_counter.load() != expected_value) {
        std::cerr << "错误：原子计数器结果不正确！" << std::endl;
    }

    // --- 测试互斥锁计数器 ---
    mutex_counter = 0; // 重置计数器
    std::cout << "\n测试 std::mutex 计数器..." << std::endl;
    auto start_mutex = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back(increment_mutex, iterations_per_thread);
    }
    for (auto &t : threads) {
        t.join();
    }
    auto end_mutex = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> duration_mutex = end_mutex - start_mutex;
    threads.clear(); // 清空线程向量

    std::cout << "互斥锁计数器最终值: " << mutex_counter << std::endl;
    std::cout << "互斥锁计数器耗时: " << duration_mutex.count() << " ms" << std::endl;
    if (mutex_counter != expected_value) {
        std::cerr << "错误：互斥锁计数器结果不正确！" << std::endl;
    }

    std::cout << "\n预期值: " << expected_value << std::endl;

    return 0;
}

/*
编译命令 (需要 C++11):
g++ your_code_name.cpp -o atomic_counter_app -std=c++11 -pthread -O2
或者
clang++ your_code_name.cpp -o atomic_counter_app -std=c++11 -pthread -O2
(推荐开启优化 -O2 或 -O3 以观察性能差异)
*/
// ``` **体现的概念 ** : ***原子操作 ** : `std::atomic<T>` 提供了对类型 `T`
// 的原子访问和修改，保证操作的不可分割性，从而避免数据竞争。 ***无锁编程(Lock - Free) * * : 对于 `std::atomic`
// 支持的类型和操作，如果底层硬件支持相应的原子指令（如
// `fetch_add`），则可以实现无锁同步。这通常比使用互斥锁有更低的开销和更好的伸缩性，尤其是在低到中等争用情况下。
// ***内存序(Memory Order) * * : `std::memory_order` 参数（如 `relaxed`, `acquire`, `release`,
// `seq_cst`）控制原子操作如何与其他内存访问（包括其他原子和非原子访问）排序，是编写正确且高效的无锁代码的关键。`relaxed`
// 提供最少的保证，`seq_cst`（默认）提供最强的顺序一致性