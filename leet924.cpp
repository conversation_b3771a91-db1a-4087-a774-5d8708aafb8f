#include<bits/stdc++.h>
using namespace std;
class unionfind
{
public:
    vector<int> fa;
    vector<int> sz;
    unionfind(int n)
    {
        sz=vector<int>(n,1);
        fa=vector<int>(n);
        iota(fa.begin(),fa.end(),0);
        
    }
    int find(int x)
    {
        if(fa[x]==x)
        {
            return x;
        }
        return fa[x]=find(fa[x]);
    }
    void merge(int x,int y)
    {
        int fx=find(x);
        int fy=find(y);
        if(fx==fy) return;
        if(sz[fx]>sz[fy])
        {
            fa[fy]=fx;
            sz[fx]+=sz[fy];
        }
        else
        {
            fa[fx]=fy;
            sz[fy]+=sz[fx];
        }
    }
    int getsize(int x)
    {
        return sz[x];
    }
    
};
class Solution {
public:
    int minMalwareSpread(vector<vector<int>>& graph, vector<int>& initial) {
        int n=graph.size();
        unionfind uf(n);
        for(int i=0;i<n;i++)
        {
            for(int j=0;j<n;j++)
            {
                if(graph[i][j]==1)
                {
                    uf.merge(i,j);
                }
            }
        }
        vector<int> cnt(n);
        for(int x:initial)
        {
            cnt[uf.find(x)]++;
        }
        int ans=*min_element(initial.begin(),initial.end()),maxsize=0;
        for(int i=0;i<initial.size();i++)
        {
           int index=initial[i];
           int fx=uf.find(index);
           if(cnt[fx]==1)
           {

                
                if(uf.getsize(fx)>maxsize)
                {
                    ans=index;
                    maxsize=uf.getsize(fx);

                }
                else if(uf.getsize(fx)==maxsize && index<ans)
                {
                    ans=index;
                }
           }
        }
        return ans;

    }
};
int main()
{
    vector<vector<int>> graph={{1,1,0},{1,1,0},{0,0,1}};
    vector<int> initial={0,1,2};
    Solution s;
    cout<<s.minMalwareSpread(graph,initial);
    return 0;
}