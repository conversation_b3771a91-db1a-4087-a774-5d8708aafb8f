#include <fstream>
#include <iostream>
#include <unordered_map>
#include <string>
#include <vector>
#include <sstream>
#include <cctype>
#include <algorithm>

using namespace std;

struct TrieNode {
    unordered_map<char, TrieNode*> children;
    int count = 0;
    bool isEndOfWord = false;
};

void insert(TrieNode* root, const string &word) {
    TrieNode* node = root;
    for (char c : word) {
        if (node->children.find(c) == node->children.end()) {
            node->children[c] = new TrieNode();
        }
        node = node->children[c];
    }
    node->isEndOfWord = true;
    node->count++;
}

void collectWords(TrieNode* node, const string &word, vector<pair<string, int>> &words) {
    if (node->isEndOfWord) {
        words.push_back(make_pair(word, node->count));
    }
    for (auto &p : node->children) {
        collectWords(p.second, word + p.first, words);
    }
}

int main() {
    TrieNode* root = new TrieNode();
    ifstream file("in.txt");
    string line;

    if (!file.is_open()) {
        cerr << "Unable to open file in.txt" << endl;
        return 1;
    }
    while (getline(file, line)) {
        istringstream stream(line);
        string word;
        while (stream >> word) {
            string cleanedWord;
            for (char c : word) {
                if (isalpha(c)) {
                    cleanedWord += tolower(c);
                }
                else
                {
                    if(!cleanedWord.empty())
                    {
                      insert(root, cleanedWord);
                      cleanedWord.clear();
                    }
               }
            }
            if (!cleanedWord.empty()) {
                insert(root, cleanedWord);
            }
        }
    }
    file.close();
    vector<pair<string, int>> words;
    collectWords(root, "", words);
    sort(words.begin(), words.end(), [](const pair<string, int>& a, const pair<string, int>& b) {
        if(a.second == b.second)
        {
          return a.first<b.first;
        }
        return a.second > b.second;
    });
    int i=0;
    for (const auto &pair : words) {
        i++;
        cout << pair.first << " " << pair.second << endl;
        if(i == 100) break;
    }
    return 0;
}