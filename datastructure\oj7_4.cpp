#include <iostream>
#include <vector>
#include <sstream>
#include <climits>

using namespace std;

int main()
{
    int q, n;
    while (true)
    {
        stringstream ss;
        vector<int> v;
        string s;
        cin >> s >> n;
        if (s.length() > 3)
            s.erase(s.end() - 3); // 去掉小数点
        else
            s.append("00"); // 再扩大100倍，方便处理
        ss << s;
        ss >> q;
        if (n == 0)
            return 0;
        int m, price, sum, temN = n;
        char ch;
        while (temN--)
        {
            sum = 0;
            bool b = false;
            cin >> m;
            while (m--)
            {
                ss.str(string());
                ss.clear();
                cin >> ch;
                if (ch >= 'A' && ch <= 'C')
                {
                    cin.get();
                    string temp;
                    cin >> temp;
                    if (temp.length() > 3)
                        temp.erase(temp.end() - 3);
                    else
                        temp.append("00");
                    if (temp == "000")
                        goto flag;
                    ss << temp;
                    ss >> price;
                    if (price > 60000 || price > q)
                        goto flag; // 剪枝优化，不需要继续读取
                    sum += price;
                    if (sum > 100000)
                        goto flag;
                }
                else
                {
                flag:
                    string tempS;
                    getline(cin, tempS); // 输入流cin清除缓冲区
                    b = true;
                    break;
                }
            }
            if (!b)
            {
                v.push_back(sum);
            }
        }
        if (v.empty())
        {
            cout << "0.00" << endl;
            continue;
        }
        int dp[q + 1] = {0};
        for (int i = 0; i <= q; ++i)
        {
            if (i < v[0])
                dp[i] = 0;
            else
                dp[i] = v[0];
        } // 初始化第一行
        for (int i = 1; i < v.size(); ++i)
        {
            for (int j = q; j >= v[i]; --j)
            {
                dp[j] = max(dp[j - v[i]] + v[i], dp[j]); // dp过程，此处采用滚动数组降维
            }
        }
        /*for (int i = 1; i < v.size(); ++i) {
            for (int j = 1; j <= q; ++j) {
                if (v[i] > j) dp[i][j] = dp[i - 1][j];
                else dp[i][j] = max(dp[i - 1][j - v[i]] + v[i], dp[i - 1][j]);
            }
        }       此处是原始方案，未降维*/
        string res = to_string(dp[q]);
        res.insert(res.end() - 2, '.');
        cout << res << endl;
    }
}