#include <queue>
#include<iostream>
using namespace std;
class Solution {
public:
    int nthUglyNumber(int n) {
        priority_queue<int,vector<int>,greater<int> > q;
        q.push(1);
        int temp;
        for(int i=0;i<n;i++)
        {
            temp=q.top();
            q.pop();
            for(int i=0;i<3;i++)
            {
                q.push(temp*2);
                q.push(temp*3);
                q.push(temp*5);
            }
        }
        return temp;

    }
};
int main()
{
    Solution s;;
    auto res=s.nthUglyNumber(10);
    cout<<res<<endl;
    return 0;
}