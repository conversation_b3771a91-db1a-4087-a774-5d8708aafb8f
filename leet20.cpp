#include<bits/stdc++.h>
using namespace std;
class Solution {
public:
    bool isValid(string s) {
        stack<char> sak;
        int len=s.size();
        for(int i=0;i<len;i++)
        {
            switch (s[i])
            {
            case '(':
            case '[':
            case '{':
                sak.push(s[i]);
                break;
            case '}':
                if(sak.empty() || sak.top()!='{')
                {
                    return false;
                }
                sak.pop();
                break;
            case ')':
                if(sak.empty() || sak.top()!='(')
                {
                    return false;
                }
                sak.pop();
                break;
            case ']':
                if(sak.empty() || sak.top()!='[')
                {
                    return false;
                }
                sak.pop();
                break;
            default:
                break;
            }
        }
        return sak.empty();

    }
};
int main()
{
    Solution sol;
    cout<<sol.isValid("((((()))))");
    return 0;
}