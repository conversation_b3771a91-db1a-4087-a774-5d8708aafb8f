#include<vector>
#include<string>
#include<iostream>
using namespace std;
class Solution {
public:
    int longestValidParentheses(string s) {
        int size=s.size();
        vector<int>dp(size,0);
        int maxval=0;
        for(int i=1;i<size;i++)
        {
            
            if(s[i]==')')
            {
                if(s[i-1]=='(')
                {
                    dp[i]=2;
                    if(i>=2)
                    {
                        dp[i]+=dp[i-2];
                    }
                }
                else
                {
                    if(dp[i-1]>0)
                    {
                        if(i-1-dp[i-1]>=0 && s[i-1-dp[i-1]]=='(')
                        {
                            dp[i]=dp[i-1]+2;
                            if(i-2-dp[i-1]>=0)
                            {
                                dp[i]+=dp[i-2-dp[i-1]];
                            }
                        }
                    }

                }
            }
            maxval=max(maxval,dp[i]);
        }
        return maxval;


    }
};
int main()
{
    Solution sol;
    int n= sol.longestValidParentheses("");
    cout<<n<<endl;
    return 0;
}