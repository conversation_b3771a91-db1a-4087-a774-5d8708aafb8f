#include <emmintrin.h> // For _mm_clflush
#include <stdint.h>
#include <stdio.h>
#include <x86intrin.h> // For __rdtsc

// 定义一个用于探测的数组
#define ARRAY_SIZE (10 * 1024 * 1024) // 足够大，不易完全放入缓存
uint8_t probe_array[ARRAY_SIZE];

// 用于测量时间差的阈值（需要根据实际硬件调整）
// 这个值非常依赖于具体的CPU和系统状态
#define CACHE_HIT_THRESHOLD (150) // 示例值，远小于缓存缺失的时间

// 读取CPU时间戳计数器
inline uint64_t rdtsc() {
    unsigned int lo, hi;
    __asm__ volatile("rdtsc" : "=a"(lo), "=d"(hi));
    return ((uint64_t)hi << 32) | lo;
}

int main() {
    // 初始化数组，确保物理页面被分配
    for (int i = 0; i < ARRAY_SIZE; ++i) {
        probe_array[i] = 1;
    }

    // 选择一个探测点（例如数组中间的某个元素）
    // 乘以64是为了确保每次探测的地址落在不同的缓存行（通常为64字节）
    // 但在这个简单例子中，我们只关注一个特定地址
    size_t probe_index = ARRAY_SIZE / 2;
    uint8_t *target_addr = &probe_array[probe_index];

    uint64_t t1, t2;
    uint64_t miss_time, hit_time;

    // --- 1. 测量缓存缺失 (Cache Miss) 时间 ---
    // 清除目标地址所在的缓存行，使其变“冷”
    _mm_mfence(); // 内存屏障，确保之前的写操作完成
    _mm_clflush(target_addr);
    _mm_mfence(); // 确保clflush完成

    // 测量访问被清除缓存行的时间
    t1 = rdtsc();
    _mm_lfence();                          // 序列化指令，防止rdtsc乱序执行
    volatile uint8_t value = *target_addr; // 访问目标地址
    _mm_lfence();                          // 确保访问完成
    t2 = rdtsc();
    miss_time = t2 - t1;

    // --- 2. 测量缓存命中 (Cache Hit) 时间 ---
    // 再次访问同一地址，此时它应该在缓存中（变“热”）
    t1 = rdtsc();
    _mm_lfence();
    volatile uint8_t value2 = *target_addr; // 再次访问
    _mm_lfence();
    t2 = rdtsc();
    hit_time = t2 - t1;

    // --- 3. 再次测量缓存缺失时间 (验证) ---
    _mm_mfence();
    _mm_clflush(target_addr);
    _mm_mfence();

    t1 = rdtsc();
    _mm_lfence();
    volatile uint8_t value3 = *target_addr;
    _mm_lfence();
    t2 = rdtsc();
    uint64_t miss_time_2 = t2 - t1;

    printf("Target Address: %p\n", (void *)target_addr);
    printf("Time for Cache Miss (Cold): %lu cycles\n", miss_time);
    printf("Time for Cache Hit (Hot) : %lu cycles\n", hit_time);
    printf("Time for Cache Miss 2    : %lu cycles\n", miss_time_2);
    printf("--- Threshold: %d cycles ---\n", CACHE_HIT_THRESHOLD);

    if (hit_time < CACHE_HIT_THRESHOLD) {
        printf("Cache Hit detected (Time < Threshold).\n");
    } else {
        printf("Cache Hit NOT detected reliably (Time >= Threshold).\n");
    }

    if (miss_time > CACHE_HIT_THRESHOLD && miss_time_2 > CACHE_HIT_THRESHOLD) {
        printf("Cache Miss detected (Time > Threshold).\n");
    } else {
        printf("Cache Miss NOT detected reliably (Time <= Threshold).\n");
    }

    // 增加一个无用的变量使用，防止编译器优化掉value, value2, value3
    volatile uint8_t dummy = value + value2 + value3 + probe_array[0];
    (void)dummy; // 防止 unused variable 警告

    return 0;
}