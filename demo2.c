#include <stdarg.h>
#include <stdio.h>

void my_printf(const char *format, ...)
{
    va_list args;
    va_start(args, format);

    const char *p = format;
    while (*p != '\0')
    {
        if (*p == '%' && *(p + 1) != '\0')
        {
            p++;
            switch (*p)
            {
            case 'd':
            {
                int i = va_arg(args, int);
                printf("%d", i);
                break;
            }
            case 'f':
            {
                double d = va_arg(args, double);
                printf("%f", d);
                break;
            }
            case 'c':
            {
                char c = (char)va_arg(args, int); // char 提升为 int
                printf("%c", c);
                break;
            }
            case 's':
            {
                char *s = va_arg(args, char *);
                printf("%s", s);
                break;
            }
            default:
                putchar('%');
                putchar(*p);
                break;
            }
        }
        else
        {
            putchar(*p);
        }
        p++;
    }

    va_end(args);
}

int main()
{
    my_printf("Hello %s, your score is %d%%\n", "Alice", 90);
    return 0;
}
