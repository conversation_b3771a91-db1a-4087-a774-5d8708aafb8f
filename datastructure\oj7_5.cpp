#include <bits/stdc++.h>
using namespace std;

int main()
{
    int M;
    ios::sync_with_stdio(false);
    cin.tie(0);
    cout.tie(0);

    while (true)
    {
        cin >> M;
        if (M == 0)
        {
            break;
        }

        int K;
        cin >> K;
        vector<int> coins(K);
        for (int i = 0; i < K; ++i)
        {
            cin >> coins[i];
        }

        vector<int> dp(M + 1, INT_MAX);
        dp[0] = 0;

        // Dynamic programming to find the minimum number of coins
        for (int i = 1; i <= M; ++i)
        {
            for (int coin : coins)
            {
                if (i >= coin && dp[i - coin] != INT_MAX)
                {
                    dp[i] = min(dp[i], dp[i - coin] + 1);
                }
            }
        }

        if (dp[M] == INT_MAX)
        {
            cout << "Impossible" << endl;
        }
        else
        {
            cout << dp[M] << endl;
        }
    }

    return 0;
}