#include <iostream>
#include <semaphore>
#include <thread>

std::binary_semaphore sem(0); // 初始计数0

void worker() {
    if (sem.try_acquire()) {
        std::cout << "Worker acquired semaphore\n";
    } else {
        std::cout << "Worker failed to acquire semaphore\n";
    }
}

int main() {
    std::thread t(worker);

    std::this_thread::sleep_for(std::chrono::seconds(1));
    sem.release(); // 主线程释放信号量

    t.join();
    return 0;
}