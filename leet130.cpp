#include <vector>
using namespace std;
class Solution {
public:
    void solve(vector<vector<char>>& board) {
        m=board.size();
        n=board[0].size();
        vector<vector<int>> visited(m, vector<int>(n,0));
        for(int i=0;i<m;++i)
        {
            for(int j=0;j<n;++j)
                if(board[i][j]=='O' && (i==m-1 || i==0 || j==n-1 || j==0))
                {
                    dfs(board,i,j,visited);
                }
        }
        for(int i=0;i<m;++i)
        {
            for(int j=0;j<n;++j)
                if(!visited[i][j]) board[i][j]='X';
        }


    }
private:
    int m;
    int n;
    int dx[4]={0,1,0,-1};
    int dy[4]={1,0,-1,0};
    void dfs(vector<vector<char>>& board, int i, int j ,vector<vector<int>>& visited)
    {
        if(i<0 || i>=m || j<0 || j>=n) return;
        if(board[i][j]=='X') return;
        if(visited[i][j]) return;
        visited[i][j]=true;
        for(int k=0;k<4;++k)
        {
            int x=dx[k]+i, y=dy[k]+j;
            dfs(board,x,y,visited);
        }
    }

};
int main()
{
    Solution s;
    vector<vector<char>> board={{'O','O'},{'O','O'}};
    s.solve(board);
    return 0;
}