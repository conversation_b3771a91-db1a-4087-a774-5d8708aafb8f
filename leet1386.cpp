#include <vector>
#include <algorithm>
using namespace std;
class Solution
{
public:
    int findTheDistanceValue(vector<int> &arr1, vector<int> &arr2, int d)
    {
        int ans = 0;
        sort(arr2.begin(), arr2.end());
        for (int i = 0; i < arr1.size(); i++)
        {
            if (binary_search(arr2, arr1[i] - d, arr1[i] + d) == false)
            {
                ans++;
            }
        }
        return ans;
    }
    bool binary_search(vector<int> &arr, int low, int high)
    {
        int l = 0, r = arr.size() - 1;
        while (l <= r)
        {
            int mid = l + (r - l) / 2;
            if (low <= arr[mid] && arr[mid] <= high)
            {
                return true;
            }
            else if (arr[mid] < low)
            {
                l = mid + 1;
            }
            else if (arr[mid] > high)
            {
                r = mid - 1;
            }
        }
        return false;
    }
};