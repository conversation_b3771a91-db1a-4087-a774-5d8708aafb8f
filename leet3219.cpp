#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    long long minimumCost(int m, int n, vector<int> &horizontalCut, vector<int> &verticalCut)
    {
        int h_cnt = 1;
        int v_cnt = 1;
        int i = 0;
        int j = 0;
        long long ans = 0;
        sort(horizontalCut.begin(), horizontalCut.end(), greater<int>());
        sort(verticalCut.begin(), verticalCut.end(), greater<int>());
        while (i < m - 1 || j < n - 1)
        {
            if (j == n - 1 || i < m - 1 && horizontalCut[i] > verticalCut[j])
            {
                ans += v_cnt * horizontalCut[i];
                h_cnt++;
                i++;
            }
            else
            {
                ans += h_cnt * verticalCut[j];
                v_cnt++;
                j++;
            }
                }
        return ans;
    }
};