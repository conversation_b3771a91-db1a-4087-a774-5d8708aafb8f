#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    int maxOperations(vector<int> &nums)
    {
        int s = nums[0] + nums[1];
        int cnt = 1;
        for (int i = 2; i + 1 < nums.size(); i += 2)
        {
            if (s == nums[i] + nums[i + 1])
            {
                cnt++;
            }
            else
            {
                break;
            }
        }
        return cnt;
    }
};
int main()
{
    Solution s;
    vector<int> nums = {3, 2, 1, 4, 1};
    cout << s.maxOperations(nums);
    return 0;
}