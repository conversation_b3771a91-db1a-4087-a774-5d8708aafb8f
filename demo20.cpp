#include <chrono> // for std::chrono::seconds
#include <future> // for std::async, std::future
#include <iostream>
#include <numeric> // for std::accumulate
#include <thread>  // for std::this_thread
#include <vector>

// 一个可能耗时的计算函数
long long sum_vector(const std::vector<int> &vec) {
    std::cout << "[计算线程 " << std::this_thread::get_id() << "] 开始计算..." << std::endl;
    // 模拟耗时工作
    std::this_thread::sleep_for(std::chrono::seconds(2));
    long long sum = std::accumulate(vec.begin(), vec.end(), 0LL); // 使用 long long 避免溢出
    std::cout << "[计算线程 " << std::this_thread::get_id() << "] 计算完成。" << std::endl;
    return sum;
}

int main() {
    std::vector<int> data = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};

    std::cout << "[主线程 " << std::this_thread::get_id() << "] 启动异步计算..." << std::endl;

    // 使用 std::async 启动异步任务
    // std::launch::async 策略提示库在新线程中运行任务 (如果资源允许)
    // std::launch::deferred 策略会让任务延迟到 .get() 或 .wait() 被调用时才在当前线程执行
    // 默认策略 (std::launch::async | std::launch::deferred) 由实现决定
    std::future<long long> result_future = std::async(std::launch::async, sum_vector, data);

    std::cout << "[主线程 " << std::this_thread::get_id() << "] 在等待结果期间可以做其他事情..." << std::endl;
    // 模拟主线程的其他工作
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    std::cout << "[主线程 " << std::this_thread::get_id() << "] 其他事情处理完毕。" << std::endl;

    std::cout << "[主线程 " << std::this_thread::get_id() << "] 等待并获取计算结果..." << std::endl;
    try {
        // 调用 future::get() 会阻塞当前线程，直到异步任务完成并返回结果
        // 注意：对于非 shared_future，get() 只能调用一次
        long long sum = result_future.get();
        std::cout << "[主线程 " << std::this_thread::get_id() << "] 收到结果: " << sum << std::endl;
    } catch (const std::exception &e) {
        // 如果异步任务抛出异常，get() 会重新抛出该异常
        std::cerr << "[主线程 " << std::this_thread::get_id() << "] 异步计算出错: " << e.what() << std::endl;
    }

    std::cout << "[主线程 " << std::this_thread::get_id() << "] 程序结束。" << std::endl;
    return 0;
}

/*
编译命令 (需要 C++11):
g++ your_code_name.cpp -o async_future_app -std=c++11 -pthread
或者
clang++ your_code_name.cpp -o async_future_app -std=c++11 -pthread
*/
// ``` **体现的概念 *
//     * : ***异步执行 ** : `std::async` 允许将函数调用提交到后台执行（可能在新的线程中）。 ***结果传递 ** : `std::
//             future` 提供了一个通道，用于在未来某个时间点安全地、同步地获取异步操作的结果或异常。调用 `get()`
//             会阻塞，直到结果就绪，这本身就是一种同步