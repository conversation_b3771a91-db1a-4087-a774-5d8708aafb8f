#include<vector>
#include<algorithm>
using namespace std;
class Solution {
public:
    vector<int> findPeakGrid(vector<vector<int>>& mat) {
        int len=mat.size();
        int l=0,r=len-1;
        while(l<r)
        {
            int mid=l+r>>1;
            int j=index_of_max(mat[mid]);
            if(mat[mid][j]>mat[mid+1][j])
            {
                r=mid;
            }
            else
            {
                l=mid+1;
            }

        }
        return vector<int>{l,index_of_max(mat[l])};


        
    }
    int index_of_max(vector<int> &v)
    {
        return max_element(v.begin(),v.end())-v.begin();
    }
};
int main()
{
    Solution sol;
    vector<vector<int>> v{{10,20,15},{21,30,14},{7,16,32}};
    auto res=sol.findPeakGrid(v);
    return 0;
}