#include <bits/stdc++.h>
using namespace std;
int expand(string s, int l, int r)
{
    while (l >= 0 && r < s.length() && s[l] == s[r])
    {
        l--;
        r++;
    }
    return r - l - 1;
}
int main()
{
    ios::sync_with_stdio(false);
    cin.tie(0);
    int t;
    cin >> t;
    while (t--)
    {
        string s;
        int start = 0, end = 0;
        cin >> s;
        int n = s.size();
        for (int i = 0; i < n; i++)
        {
            int len1 = expand(s, i, i);
            int len2 = expand(s, i, i + 1);
            int len = max(len1, len2);
            if (len > end - start + 1)
            {

                start = i - (len - 1) / 2;
                end = i + len / 2;
            }
        }
        cout << start << " " << end - start + 1 << endl;
    }
    return 0;
}