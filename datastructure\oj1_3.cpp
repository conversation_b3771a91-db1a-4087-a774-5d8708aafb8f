#include<queue>
#include<iostream>
using namespace std;
void find(vector<int> &v,vector<queue<int>> &q,int &newout)
{
    for(int i=0;i<q.size();i++)
    {
        if(q[i].front()==newout)
        {
            q[i].pop();
            newout++;
            find(v,q,newout);
        }
    }
}
int main()
{
    vector<int> v;
    vector<queue<int>> q;
    int t;
    while(cin>>t)
    {
        v.push_back(t);
    }
    int newout=1;
    int cnt=1;
    for(int i=v.size()-1;i>=0;i--)
    {
        if(v[i]==newout)
        {
            newout++;
        }
        else
        {
           find(v,q,newout);
           if(q.size())
           {
                int index=-1;
                for(int j=0;j<q.size();j++)
                {
                    if(q[j].back()<v[i])
                    {
                        if(index==-1) index=j;
                        else
                        {
                            if(q[j].back()>q[index].back()) index=j;
                        }
                    }
                }
                if(index!=-1)
                {
                    q[index].emplace(v[i]);
                }
                else
                {
                    queue<int>tmp;
                    tmp.emplace(v[i]);
                    q.emplace_back(tmp);
                    cnt++;
                }

           }
           else
           {
                queue<int>tmp;
                tmp.emplace(v[i]);
                q.emplace_back(tmp);
                cnt++;
           }
        }
    }
    cout<<cnt<<endl;
    return 0;

}