#include <bits/stdc++.h>
using namespace std;
class Solution {
  public:
    long long numberOfPairs(vector<int> &nums1, vector<int> &nums2, int k) {
        long long ans = 0;
        unordered_map<int, int> cnt;
        for (int num : nums1) {
            if (num % k) {
                continue;
            } else {
                num /= k;
                for (int i = 1; i * i <= num; i++) {
                    if (num % i) {
                        continue;
                    } else {
                        cnt[i]++;
                        if (i * i < num) {
                            cnt[num / i]++;
                        }
                    }
                }
            }
        }
        for (int num : nums2) {
            if (cnt.count(num)) {
                ans += cnt[num];
            }
        }
        return ans;
    }
};
int main() {
    Solution s;
    vector<int> nums1 = {1, 2, 4, 12};
    vector<int> nums2 = {2, 4};
    int k = 3;
    cout << s.numberOfPairs(nums1, nums2, k) << endl;
    return 0;
}