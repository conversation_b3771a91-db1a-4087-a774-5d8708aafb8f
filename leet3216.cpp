#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    string getSmallestString(string s)
    {
        for (int i = 0; i < s.size() - 1; i++)
        {
            int x = s[i] - 'a';
            int y = s[i + 1] - 'a';
            if ((x % 2 == y % 2) && (x > y))
            {
                swap(s[i], s[i + 1]);
                break;
            }
        }
        return s;
    }
};