#include <bits/stdc++.h>
using namespace std;
struct ListNode
{
    int val;
    ListNode *next;
    ListNode() : val(0), next(nullptr) {}
    ListNode(int x) : val(x), next(nullptr) {}
    ListNode(int x, ListNode *next) : val(x), next(next) {}
};

class Solution
{
public:
    ListNode *swapNodes(ListNode *head, int k)
    {
        int n = 0;
        for (ListNode *i = head; i != nullptr; i = i->next)
        {
            n++;
        }
        int a = k;
        int b = n - k + 1;
        ListNode *i = head;
        ListNode *j = head;
        while (a > 1)
        {
            i = i->next;
            a--;
        }
        while (b > 1)
        {
            j = j->next;
            b--;
        }
        swap(i->val, j->val);
        return head;
    }
};