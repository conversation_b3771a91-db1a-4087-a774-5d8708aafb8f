#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    int lenLongestFibSubseq(vector<int> &arr)
    {
        int n = arr.size();
        vector<vector<int>> dp(n, vector<int>(n, 2));
        unordered_map<int, int> m;
        int ans = 0;
        for (int i = 0; i < n; i++)
        {
            m[arr[i]] = i;
        }
        for (int j = 0; j < n; j++)
        {
            for (int k = j + 1; k < n; k++)
            {
                int num = arr[k] - arr[j];
                if (m.find(num) != m.end() && num < arr[j])
                {
                    int index = m[num];
                    dp[j][k] = dp[index][j] + 1;
                    ans = max(ans, dp[j][k]);
                }
            }
        }
        return ans >= 3 ? ans : 0;
    }
};