#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    bool reportSpam(vector<string> &message, vector<string> &bannedWords)
    {
        // 创建哈希集合存储禁用词
        unordered_set<string> banned(bannedWords.begin(), bannedWords.end());

        // 统计匹配的禁用词数量
        int count = 0;
        for (const string &word : message)
        {
            if (banned.count(word))
            {
                count++;
                if (count >= 2)
                {
                    return true;
                }
            }
        }

        return false;
    }
};