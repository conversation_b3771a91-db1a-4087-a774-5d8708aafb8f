#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    void nextPermutation(vector<int> &nums)
    {
        int n = nums.size();
        int k = -1;
        for (int i = n - 1; i - 1 >= 0; i--)
        {
            if (nums[i - 1] < nums[i])
            {
                k = i - 1;
                break;
            }
        }
        if (k == -1)
        {
            sort(nums.begin(), nums.end());
            return;
        }
        int l = -1;
        for (int i = n - 1; i >= 0; i--)
        {
            if (nums[i] > nums[k])
            {
                l = i;
                break;
            }
        }
        swap(nums[k], nums[l]);
        reverse(nums.begin() + k + 1, nums.end());
        return;
    }
};