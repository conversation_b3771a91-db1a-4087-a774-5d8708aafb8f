#include<vector>
#include<queue>
#include<iostream>
using namespace std;
int main()
{
    priority_queue<int,vector<int>,greater<int>> q;
    int n;
    int x;
    cin>>n;
    for(int i=0;i<n;i++)
    {
        cin>>x;
        q.push(x);
    }
    int sum=0;
    for(int i=0;i<n-1;i++)
    {
        int x=q.top();q.pop();
        int y=q.top();q.pop();
        sum+=x+y;
        q.push(x+y);
    }
    cout<<sum<<endl;
    return 0;

}