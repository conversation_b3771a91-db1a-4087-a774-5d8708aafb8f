#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    vector<int> resultsArray(vector<int> &nums, int k)
    {
        int cnt = 0;
        vector<int> res(nums.size() - k + 1, -1);
        for (int i = 0; i < nums.size(); i++)
        {
            cnt = i == 0 || nums[i] == nums[i - 1] + 1 ? cnt + 1 : 1;
            if (cnt >= k)
            {
                if (i - k + 1 >= 0)
                {
                    res[i - k + 1] = nums[i];
                }
            }
        }
        return res;
    }
};
int main()
{
    Solution s;
    vector<int> nums = {1, 2, 3, 4, 3, 2, 5};
    int k = 3;
    vector<int> res = s.resultsArray(nums, k);
    for (int i = 0; i < res.size(); i++)
    {
        cout << res[i] << " ";
    }
    return 0;
}