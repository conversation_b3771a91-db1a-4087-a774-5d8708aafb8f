#include <vector>
#include <iostream>
#include <unordered_set>
#include <unordered_map>
using namespace std;
class findset
{
public:
    unordered_map<int, int> parent;
    unordered_map<int, int> rank;
    findset(vector<int> &nums)
    {
        for (auto &i : nums)
        {
            parent[i] = i;
            rank[i] = 1;
        }
    }
    int find(int x)
    {
        if (parent[x] == x)
            return x;
        else
            return parent[x] = find(parent[x]);
    }
    void merge(int x, int y)
    {
        int px = find(x);
        int py = find(y);
        if (px == py)
        {
            return;
        }
        else
        {
            if (rank[px] > rank[py])
            {
                parent[py] = px;
            }
            else if (rank[px] < rank[py])
            {
                parent[px] = py;
            }
            else
            {
                parent[py] = px;
                rank[px]++;
            }
        }
    }
};
class Solution
{
public:
    int longestConsecutive(vector<int> &nums)
    {
        ::findset fs(nums);
        unordered_set<int> st;
        for (auto &i : nums)
        {
            st.insert(i);
            if (st.count(i - 1))
            {
                fs.merge(i, i - 1);
            }

            if (st.count(i + 1))
            {
                fs.merge(i, i + 1);
            }
        }
        unordered_map<int, int> cnt;
        for (auto &i : st)
        {
            cnt[fs.find(i)]++;
        }
        int max = 0;
        for (auto [k, v] : cnt)
        {
            if (v > max)
            {
                max = v;
            }
        }
        return max;
    };
};

int main()
{
    vector<int> v{0, 3, 7, 2, 5, 8, 4, 6, 0, 1};
    Solution s;
    auto res = s.longestConsecutive(v);
    cout << res;
    return 0;
}