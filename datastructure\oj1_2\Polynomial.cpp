#include "Polynomial.h"
void Polynomial:: addterm(int coefficient, int exponent)
{
    this->terms.push_back({coefficient, exponent});
}
Polynomial Polynomial:: add(const Polynomial &other) const
{
    Polynomial result;
    // 添加当前多项式的所有项到结果多项式中
    auto it1=this->terms.begin();
    auto it2=other.terms.begin();
    while(it1!=this->terms.end() && it2!=other.terms.end())
    {
        if(it1->second>it2->second)
        {
            result.addterm(it1->first, it1->second);
            it1++;
        }
        else if(it1->second<it2->second)
        {
            result.addterm(it2->first, it2->second);
            it2++;
        }
        else
        {
            int sum=it1->first+it2->first;
            if(sum!=0)
            {
                result.addterm(sum, it1->second);
            }
            it1++;
            it2++;
        }

    }
    while(it1!=this->terms.end())
    {
        result.addterm(it1->first, it1->second);
        it1++;
    }
    while(it2!=other.terms.end())
    {
        result.addterm(it2->first, it2->second);
        it2++;
    }
    return result;
}
 Polynomial  Polynomial:: operator+(const Polynomial &other) const
 {
    return add(other);
 }
 int Polynomial::getsize() const
 {
    return this->terms.size();
 }