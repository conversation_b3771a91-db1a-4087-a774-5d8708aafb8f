#include<queue>
#include<iostream>
using namespace std;
class Node
{
public:
    Node(int w): weight(w),left(NULL),right(NULL){}
    int weight;
    Node*left;
    Node*right;
};

class compare
{
public:
    bool operator()(Node*a, Node*b)
    {
        return a->weight>b->weight;
    }
};
int dfs(Node* root,int depth)
{
    if(root==NULL) return 0;
    if(root->left==NULL && root->right==NULL) return depth*root->weight;
    return dfs(root->left,depth+1)+dfs(root->right,depth+1);
}
void deleteTree(Node* node) {
    if (node == NULL) return;
    deleteTree(node->left);
    deleteTree(node->right);
    delete node;
}
int main()
{
    int n;
    priority_queue<Node*, vector<Node*>, compare> q;
    int w;
    while(cin>>n)
    {
        if(n==0) break;
        for(int i=0;i<n;i++)
        {
            cin>>w;
            q.push(new Node(w));
        }
        while(q.size()>1)
        {
            Node*a=q.top();
            q.pop();
            Node*b=q.top();
            q.pop();
            Node *newnode=new Node(a->weight+b->weight);
            newnode->left=a;
            newnode->right=b;
            q.push(newnode);

        }
        Node* root=q.top();
        q.pop();
        int res=dfs(root,0);
        deleteTree(root);
        cout<<res<<endl;
    }
    return 0;
}