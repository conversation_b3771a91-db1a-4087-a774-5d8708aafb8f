
  struct TreeNode {
      int val;
      TreeNode *left;
      TreeNode *right;
      TreeNode() : val(0), left(nullptr), right(nullptr) {}
      TreeNode(int x) : val(x), left(nullptr), right(nullptr) {}
      TreeNode(int x, TreeNode *left, TreeNode *right) : val(x), left(left), right(right) {}
  };
 
class Solution {
public:
    bool hasPathSum(TreeNode* root, int targetSum) {
        if(root==nullptr) return false;
        return search(root,targetSum,0);
        

    }
private:
bool search(TreeNode* root, int targetSum,int currentSum) {
    if(root==nullptr) return false;
    currentSum+=root->val;
    if(root->left==nullptr&&root->right==nullptr){
        if(currentSum==targetSum) return true;
    }
    return search(root->left,targetSum,currentSum)||search(root->right,targetSum,currentSum);
}};