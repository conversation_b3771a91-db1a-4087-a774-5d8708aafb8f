#include <numeric> // Not strictly needed for this version, but good practice
#include <vector>

using namespace std;

// --- Modular Arithmetic & Combinatorics ---
const int MOD = 1e9 + 7;
const int MAX_K = 15; // Max distinct elements ~ log2(maxValue)
vector<long long> invFact(MAX_K);

// Modular exponentiation (for inverse)
long long power(long long base, long long exp) {
    long long res = 1;
    base %= MOD;
    while (exp > 0) {
        if (exp % 2 == 1)
            res = (res * base) % MOD;
        base = (base * base) % MOD;
        exp /= 2;
    }
    return res;
}

// Modular inverse using Fermat's Little Theorem
long long modInverse(long long n) {
    return power(n, MOD - 2);
}

// Precompute inverse factorials up to MAX_K-1
void precompute_combinations() {
    invFact[0] = 1;
    long long current_fact = 1;
    for (int i = 1; i < MAX_K; ++i) {
        current_fact = (current_fact * i) % MOD;
        invFact[i] = modInverse(current_fact);
    }
}

// Calculate C(n, k) % MOD using precomputed inverse factorials
long long nCr_mod(int n, int k) {
    if (k < 0 || k > n)
        return 0;
    if (k == 0 || k == n)
        return 1;
    if (k > n / 2)
        k = n - k;
    if (k >= MAX_K)
        return 0; // k should be small

    long long numerator = 1;
    for (int i = 0; i < k; ++i) {
        numerator = (numerator * (n - i)) % MOD;
        // Handle potential negative result from (n-i) % MOD if n-i is large negative
        // Although with n >= k >= 0, n-i should be non-negative.
        // But defensive programming: if (numerator < 0) numerator += MOD;
    }
    return (numerator * invFact[k]) % MOD;
}
// --- ---

class Solution {
  private:
    // Static flag to ensure precomputation happens only once if multiple Solution objects are created
    static bool combinations_precomputed;

  public:
    int idealArrays(int n, int maxValue) {
        if (!combinations_precomputed) {
            precompute_combinations();
            combinations_precomputed = true;
        }

        // dp[x][k]: number of ideal sequences ending with x, having k distinct elements
        vector<vector<long long>> dp(maxValue + 1, vector<long long>(MAX_K, 0));

        // Base case: sequences with 1 distinct element
        for (int x = 1; x <= maxValue; ++x) {
            dp[x][1] = 1;
        }

        // DP transition: build sequences with k distinct elements from those with k-1
        for (int k = 2; k < MAX_K; ++k) {
            for (int x = 1; x <= maxValue; ++x) {
                if (dp[x][k - 1] == 0)
                    continue; // Optimization
                // Extend sequence ending in x to sequences ending in multiples of x
                for (long long m = 2;; ++m) {
                    long long next_x = x * m;
                    if (next_x > maxValue)
                        break;
                    dp[next_x][k] = (dp[next_x][k] + dp[x][k - 1]) % MOD;
                }
            }
        }

        // Calculate final answer: sum over all possible endings x and distinct counts k
        long long total_ans = 0;
        for (int x = 1; x <= maxValue; ++x) {
            for (int k = 1; k < MAX_K; ++k) {
                if (dp[x][k] == 0)
                    continue;
                // Ways to form the sequence skeleton * Ways to place k distinct numbers into n slots
                long long combinations = nCr_mod(n - 1, k - 1);
                long long term = (dp[x][k] * combinations) % MOD;
                total_ans = (total_ans + term) % MOD;
            }
        }

        return (int)total_ans;
    }
};

// Initialize static member
bool Solution::combinations_precomputed = false;