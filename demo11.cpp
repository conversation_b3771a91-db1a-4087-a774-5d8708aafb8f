#include <bits/stdc++.h>
#include <pthread.h>
#include <stdio.h>
void *thread_func(void *arg) {
    int local_var = 42;
    while (1) {
    }
    return NULL;
}

int main() {
    pthread_t tid;
    pthread_attr_t attr;
    void *stack_addr;
    size_t stack_size;

    pthread_create(&tid, NULL, thread_func, NULL);
    pthread_getattr_np(tid, &attr);
    pthread_attr_getstack(&attr, &stack_addr, &stack_size);

    printf("线程栈地址: %p, 大小: %zu\n", stack_addr, stack_size);
    // 可通过stack_addr尝试访问栈内容（需谨慎！）
    pthread_join(tid, NULL);
    return 0;
}