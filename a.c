#include <linux/module.h>
#include <linux/fs.h>
#include <linux/cdev.h>
#include <linux/uaccess.h>

#define DEVICE_NAME "mychardev"
#define BUF_LEN 80

static int major;
static char msg[BUF_LEN] = {0};
static struct cdev my_cdev;
static struct class *my_class = NULL;

static int dev_open(struct inode *inode, struct file *file)
{
       printk(KERN_INFO "Device %s opened\n", DEVICE_NAME);
       return 0;
}

static ssize_t dev_read(struct file *filp, char __user *buf, size_t len, loff_t *offset)
{
       int bytes_read = 0;
       if (*msg == 0)
              return 0;

       while (len && *msg)
       {
              put_user(*(msg++), buf++);
              len--;
              bytes_read++;
       }

       return bytes_read;
}

static ssize_t dev_write(struct file *filp, const char __user *buf, size_t len, loff_t *offset)
{
       int bytes_written = 0;
       for (bytes_written = 0; bytes_written < len && bytes_written < BUF_LEN; bytes_written++)
       {
              get_user(msg[bytes_written], buf + bytes_written);
       }

       msg[bytes_written] = '\0'; // 添加字符串结束符
       return bytes_written;
}

static int dev_release(struct inode *inode, struct file *file)
{
       printk(KERN_INFO "Device %s closed\n", DEVICE_NAME);
       return 0;
}

static struct file_operations fops = {
    .owner = THIS_MODULE,
    .open = dev_open,
    .read = dev_read,
    .write = dev_write,
    .release = dev_release,
};

static int __init mychardev_init(void)
{
       major = register_chrdev(0, DEVICE_NAME, &fops);
       if (major < 0)
       {
              printk(KERN_ALERT "Registering char device failed with %d\n", major);
              return major;
       }

       my_class = class_create(THIS_MODULE, DEVICE_NAME);
       if (IS_ERR(my_class))
       {
              unregister_chrdev(major, DEVICE_NAME);
              return PTR_ERR(my_class);
       }

       device_create(my_class, NULL, MKDEV(major, 0), NULL, DEVICE_NAME);
       printk(KERN_INFO "I was assigned major number %d. To talk to\n", major);
       return 0;
}

static void __exit mychardev_exit(void)
{
       device_destroy(my_class, MKDEV(major, 0));
       class_unregister(my_class);
       class_destroy(my_class);
       unregister_chrdev(major, DEVICE_NAME);
       printk(KERN_INFO "Goodbye, world!\n");
}

module_init(mychardev_init);
module_exit(mychardev_exit);

MODULE_LICENSE("GPL");
MODULE_AUTHOR("Your Name");
MODULE_DESCRIPTION("A simple character device driver");
MODULE_VERSION("1.0");