#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    vector<bool> canMakePaliQueries(string s, vector<vector<int>> &queries)
    {
        int n = s.size(), q = queries.size(), sum[n + 1];
        sum[0] = 0;
        for (int i = 0; i < n; i++)
        {
            int x = 1 << (s[i] - 'a');
            sum[i + 1] = sum[i] ^ x;
        }
        vector<bool> ans(q);
        for (int i = 0; i < q; i++)
        {
            auto &query = queries[i];
            int l = query[0], r = query[1], k = query[2];
            int x = sum[l] ^ sum[r + 1];
            ans[i] = __builtin_popcount(x) / 2 <= k; //__builtin_popcount(x)返回x的汉明重量，即x中1的个数
        }
        return ans;
    }
};