#include <iostream>
#include <vector>
#include <thread>
#include <queue>
#include <functional>
#include <condition_variable>
#include <future>
#include <atomic>

class ThreadPool
{
public:
    ThreadPool(size_t numThreads);
    ~ThreadPool();

    template <class F>
    auto enqueue(F &&f) -> std::future<typename std::result_of<F()>::type>;

private:
    // 工作线程
    std::vector<std::thread> workers;
    // 任务队列
    std::queue<std::function<void()>> tasks;

    // 同步机制
    std::mutex queueMutex;
    std::condition_variable condition;
    std::atomic<bool> stop;
};

// 构造函数
ThreadPool::ThreadPool(size_t numThreads) : stop(false)
{
    for (size_t i = 0; i < numThreads; ++i)
    {
        workers.emplace_back(
            [this]
            {
                for (;;)
                {
                    std::function<void()> task;

                    {
                        std::unique_lock<std::mutex> lock(this->queueMutex);
                        this->condition.wait(lock, [this]
                                             { return this->stop || !this->tasks.empty(); });

                        if (this->stop && this->tasks.empty())
                            return;

                        task = std::move(this->tasks.front());
                        this->tasks.pop();
                    }

                    task();
                }
            });
    }
}

// 析构函数
ThreadPool::~ThreadPool()
{
    {
        std::unique_lock<std::mutex> lock(queueMutex);
        stop = true;
    }
    condition.notify_all();
    for (std::thread &worker : workers)
        worker.join();
}

// 提交任务
template <class F>
auto ThreadPool::enqueue(F &&f) -> std::future<typename std::result_of<F()>::type>
{
    using return_type = typename std::result_of<F()>::type;

    auto task = std::make_shared<std::packaged_task<return_type()>>(std::forward<F>(f));

    std::future<return_type> res = task->get_future();
    {
        std::unique_lock<std::mutex> lock(queueMutex);
        // 不允许在停止后添加新任务
        if (stop)
            throw std::runtime_error("enqueue on stopped ThreadPool");

        tasks.emplace([task]()
                      { (*task)(); });
    }
    condition.notify_one();
    return res;
}

int main()
{
    ThreadPool pool(4); // 创建一个有4个线程的线程池

    // 提交一些任务
    auto result1 = pool.enqueue([]
                                {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        return 1; });

    auto result2 = pool.enqueue([]
                                {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        return 2; });

    std::cout << "Result 1: " << result1.get() << std::endl; // 输出1
    std::cout << "Result 2: " << result2.get() << std::endl; // 输出2

    return 0;
}
