	.text
	.def	@feat.00;
	.scl	3;
	.type	0;
	.endef
	.globl	@feat.00
.set @feat.00, 0
	.file	"demo2.c"
	.def	main;
	.scl	2;
	.type	32;
	.endef
	.globl	main                            # -- Begin function main
	.p2align	4, 0x90
main:                                   # @main
# %bb.0:                                # %entry
	pushq	%rbp
	subq	$48, %rsp
	leaq	48(%rsp), %rbp
	callq	__main
	movl	$0, -4(%rbp)
	xorl	%eax, %eax
	addq	$48, %rsp
	popq	%rbp
	retq
                                        # -- End function
