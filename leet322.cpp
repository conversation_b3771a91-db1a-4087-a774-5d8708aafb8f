#include <vector>
using namespace std;
class Solution
{
public:
    int coinChange(vector<int> &coins, int amount)
    {
        if (amount == 0) return 0;
        vector<int> res(amount + 1, -1);
        res[0] = 0;
        for(int i=1;i<=amount;i++)
        {
            for(int j=0;j<coins.size();j++)
            {
                if (coins[j] <= i)
                {
                    if(res[i-coins[j]] != -1)
                    {
                        if (res[i] == -1 || res[i] > res[i-coins[j]] + 1)
                            res[i] = res[i-coins[j]] + 1;
                    }
                }
            }
        }
        return res[amount];
    }
};