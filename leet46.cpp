#include<iostream>
#include<vector>
using namespace std;
class Solution {
public:
    vector<vector<int>> permute(vector<int>& nums) {
        len=nums.size();
        dfs(0,nums);
        return res;


    }
    void dfs(int t,vector<int> &nums)
    {
        if(t==len) res.push_back(nums);
        for(int i=t;i<len;i++)
        {
            swap(nums[t],nums[i]);
            dfs(t+1,nums);
            swap(nums[t],nums[i]);
        }
    }
    int len;
    vector<vector<int>> res;
};
int main()
{
    vector<int> v{1,2,3};
    Solution sol;
    auto res=sol.permute(v);
    return 0;
}