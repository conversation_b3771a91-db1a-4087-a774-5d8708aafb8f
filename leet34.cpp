#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    vector<int> searchRange(vector<int> &nums, int target)
    {
        int l = 0, r = nums.size() - 1;
        int mid;
        while (l <= r)
        {
            mid = l + (r - l) / 2;
            if (nums[mid] == target)
            {
                break;
            }
            else if (nums[mid] < target)
            {
                l = mid + 1;
            }
            else
            {
                r = mid - 1;
            }
        }
        if (l > r)
        {
            return {-1, -1};
        }
        else
        {
            int i = mid - 1, j = mid + 1;
            while (i >= 0 && nums[i] == target)
            {
                i--;
            }
            while (j < nums.size() && nums[j] == target)
            {
                j++;
            }
            return {i + 1, j - 1};
        }
    }
};
int main()
{
    vector<int> v{5, 7, 7, 8, 8, 10};
    Solution s;
    auto res = s.searchRange(v, 8);
    for (auto i : res)
    {
        cout << i << " ";
    }
    return 0;
}