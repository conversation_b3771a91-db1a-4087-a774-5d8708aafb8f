#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    int palindromePartition(string s, int k)
    {
        int n = s.size();
        vector<vector<int>> dp(n + 1, vector<int>(k + 1, INT_MAX));
        vector<vector<int>> palin(n + 1, vector<int>(n + 1, 0));

        for (int i = 0; i < n; i++)
            palin[i][i] = 0;

        for (int len = 2; len <= n; len++)
        {
            for (int i = 0; i <= n - len; i++)
            {
                int j = i + len - 1;
                if (s[i] == s[j])
                    palin[i][j] = palin[i + 1][j - 1];
                else
                    palin[i][j] = palin[i + 1][j - 1] + 1;
            }
        }

        for (int i = 0; i < n; i++)
            dp[i][1] = palin[0][i];

        for (int j = 2; j <= k; j++)
        {
            for (int i = j - 1; i < n; i++)
            {
                for (int p = j - 2; p < i; p++)
                {
                    dp[i][j] = min(dp[i][j], dp[p][j - 1] + palin[p + 1][i]);
                }
            }
        }

        return dp[n - 1][k];
    }
};