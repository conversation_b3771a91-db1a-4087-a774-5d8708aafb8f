#include<bits/stdc++.h>
using namespace std;
int a[100];
void mergearray(int a[],int first,int mid,int last,int temp[])	
{
	int i=first,j=mid+1;
	int m=mid,n=last;
	int k=0;
	while(i<=m&&j<=n)
	{
		if(a[i]<a[j])
			temp[k++]=a[i++];
		else
			temp[k++]=a[j++];
	}
	while(i<=m)
		temp[k++]=a[i++];
	while(j<=n)
		temp[k++]=a[j++];
	for(i=0;i<k;i++)
		a[first+i]=temp[i];
}
 
void mergesort(int a[],int first,int last,int temp[])	
{
	if(first<last)
	{
		int mid=(first+last)/2;
		mergesort(a,first,mid,temp);	
		mergesort(a,mid+1,last,temp);	
		mergearray(a,first,mid,last,temp);	
	}
}
 
bool MergeSort(int a[], int n)  
{  
    int *p = new int[n];  
    if (p == NULL)  
        return false;  
    mergesort(a, 0, n - 1, p);  
    delete[] p;  
    return true;  
}
int main()
{
	fstream file;
    file.open("lab1 data2.txt",ios::in);
    int i=0;
    while(file>>a[i++]);
    file.close();
    int len=sizeof(a)/sizeof(a[0]);
    MergeSort(a,len);
	for(int i=0;i<100;i++)
	{
		cout<<a[i]<<endl;
	}
    return 0;
}
