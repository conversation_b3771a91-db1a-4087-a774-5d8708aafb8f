#include<string.h>
#include<iostream>
using namespace std;
int main()
{
    string s;
    while(cin>>s)
    {
        for(int i=0;i<s.size();i++)
        {
        s[i]=tolower(s[i]);
        }
        int i=0,j=s.size()-1;
        bool flag=true;
        while(i<j)
        {
            if(s[i]!=s[j])
            {
                flag=false;
                break;
            }
            i++;
            j--;
        }
        if(flag) cout<<"yes"<<endl;
        else cout<<"no"<<endl;
    }
}