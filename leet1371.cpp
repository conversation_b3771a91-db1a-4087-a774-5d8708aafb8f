#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    int findTheLongestSubstring(string s)
    {
        int n = s.size();
        vector<int> pre(32, INT_MAX);
        pre[0] = -1;
        int x = 0;
        int ans = 0;
        for (int i = 0; i < n; i++)
        {
            switch (s[i])
            {
            case 'a':
                x ^= 1;
                break;
            case 'e':
                x ^= (1 << 1);
                break;
            case 'i':
                x ^= (1 << 2);
                break;
            case 'o':
                x ^= (1 << 3);
                break;
            case 'u':
                x ^= (1 << 4);
                break;
            default:
                break;
            }
            if (pre[x] == INT_MAX)
            {
                pre[x] = i;
            }
            else
            {
                ans = max(ans, i - pre[x]);
            }
        }
        return ans;
    }
};