#include<bits/stdc++.h>
using namespace std;
struct p
{
    int x;
    int y;
};
bool cmp(p a,p b)
{
    return a.x<b.x;
}
int main()
{
    vector<p>v;
    unordered_set<int> s;
    int n;
    cin>>n;
    int temp_x,temp_y;
    for(int i=0;i<n;i++)
    {
        cin>>temp_x>>temp_y;
        p temp;
        temp.x=temp_x;
        temp.y=temp_y;
        v.push_back(temp);
        
    }
    vector<int> sum(n);
    sort(v.begin(), v.end(), cmp);
    sum[0]=v[0].y;
    for(int i=1;i<n;i++)
    {
        sum[i]=sum[i-1]+v[i].y;
    }
   
    s.insert(v[0].x);
    int cnt_0=0;
    int cnt_1=sum[n-1];
    int max=cnt_0+cnt_1;
    int res=v[0].x;
    for(int i=1;i<n;i++)
    {
        int a=v[i].x;
        if(s.find(a)!=s.end()) continue;
        s.insert(a);
        cnt_0=i-sum[i-1];
        cnt_1=sum[n-1]-sum[i-1];
        if(max<=cnt_0+cnt_1)
        {
            max=cnt_0+cnt_1;
            res=a;
        }
        
    }
    cout<<res<<endl;
    return 0;
    
}