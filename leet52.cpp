#include<iostream>
#include<vector>
using namespace std;
class Solution {
public:
     int totalNQueens(int n) {
        len=n;
        int x[n];
        for(int i=0;i<n;i++)
        {
            x[i]=i+1;
        }
        dfs(0,x);
        return sum;

    }
private:
    int len;
    int sum=0;
    bool place(int k,int x[])
    {
        for(int i=0;i<k;i++)
        {
            if(abs(i-k)==abs(x[i]-x[k])) return false;
        }
        return true;
    }

    void dfs(int t,int x[])
    {
        if(t==len)
        {
            sum++;
            return;
        }
        for(int i=t;i<len;i++)
        {
            swap(x[i],x[t]);
            if(place(t,x))
            {
                dfs(t+1,x);
            }
            swap(x[i],x[t]);
        }
    }
    
};
int main()
{
    Solution sol;
    auto res=sol.totalNQueens(4);
    return 0;
}