#include <bits/stdc++.h>
using namespace std;
// 例2：std::vector和std::string的实际例子
int main()
{
    std::string str1 = "aacasxs";
    std::vector<std::string> vec;

    vec.push_back(str1);               // 传统方法，copy
    vec.push_back(std::move(str1));    // 调用移动语义的push_back方法，避免拷贝，str1会失去原有值，变成空字符串
    vec.emplace_back(std::move(str1)); // emplace_back效果相同，str1会失去原有值
    vec.emplace_back("axcsddcas");     // 当然可以直接接右值
}

// std::vector方法定义
void push_back(const value_type &val);
void push_back(value_type &&val);

void emplace_back(Args &&...args);
auto f(int x)
{
    return x + 3;
}