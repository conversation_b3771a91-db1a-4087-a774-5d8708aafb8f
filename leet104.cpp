#include<vector>
using namespace std;

  struct TreeNode {
      int val;
      TreeNode *left;
      TreeNode *right;
      TreeNode() : val(0), left(nullptr), right(nullptr) {}
      TreeNode(int x) : val(x), left(nullptr), right(nullptr) {}
      TreeNode(int x, TreeNode *left, TreeNode *right) : val(x), left(left), right(right) {}
  };
 
class Solution {
public:
    int maxDepth(TreeNode* root) {
        return maxdepth(root, 0);

    }
    int maxdepth(TreeNode* root, int depth) 
    {
        if(root == NULL) return depth;
        int left = maxdepth(root->left, depth+1);
        int right = maxdepth(root->right, depth+1);
        return left > right ? left : right;
    }
};