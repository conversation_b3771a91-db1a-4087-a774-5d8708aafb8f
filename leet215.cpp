#include<vector>
#include<queue>
#include<iostream>
using namespace std;
class Solution {
public:
    int findKthLargest(vector<int>& nums, int k) {
        priority_queue<int> q;
        for(int i=0;i<nums.size();i++)
        {
            q.push(nums[i]);
        }
        for(int i=0;i<k-1;i++)
        {
            q.pop();
        }
        return q.top();
        
    }
};
int main()
{
    Solution sol;
    vector<int> v{1,2,3,4,5};
    auto res=sol.findKthLargest(v,4);
    cout<<res<<endl;
    return 0;
}