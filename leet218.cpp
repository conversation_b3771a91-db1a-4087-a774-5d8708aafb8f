#include <vector>
#include <queue>
#include <bitset>
using namespace std;
class cmp
{
    bool operator()(vector<int> v1, vector<int> v2)
    {
        return v1[2] < v2[2];
    }
};
class Solution
{
public:
    vector<vector<int>> getSkyline(vector<vector<int>> &buildings)
    {
        priority_queue<vector<int>, vector<vector<int>>, cmp> q;
        vector<vector<int>> ans;
        for (int i = 0; i < buildings.size(); i++)
        {
            q.push(buildings[i]);
        }
        bitset<1ULL << 31> bs;
        for (int i = 0; i < buildings.size(); i++)
        {
            auto v = q.top();
            int left = v[0], right = v[1];
            bool flag = 0;
            int cur_left = left;
            for (int i = left; i <= right; i++)
            {
                if (flag == 0)
                {
                    if (bs.test(i) == 1)
                    {
                        if (cur_left < i - 1)
                        {
                            ans.push_back(vector<int>{cur_left, i - 1});
                        }
                        flag = 1;
                        continue;
                    }
                }
                if (flag == 1)
                {
                    if (bs[i] == 0)
                    {
                        cur_left = i;
                        flag = 0;
                        continue;
                    }
                }
                bs.set(i);
            }
        }
        return ans;
    }
};
int main()
{
    Solution sol;
    vector<vector<int>> v{{0, 2, 3}, {2, 5, 3}};
    auto res = sol.getSkyline(v);
    return 0;
}