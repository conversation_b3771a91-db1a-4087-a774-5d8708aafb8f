#include<bits/stdc++.h>
using namespace std;
class Solution {
public:
    int myAtoi(string s) {
        int i=0; 
        while(s[i]==' ')
        {
            i++;
        }
        int sign=1;
        long long res=0;
        if(s[i]=='+' || s[i]=='-')
        {
            sign=s[i]=='+'? 1:-1;
            i++;
        }
        int len=s.length();
        while(i<len && isdigit(s[i]))
        {
          
                res=res*10+(s[i]-'0');
                if(res>INT_MAX)
                {
                    return sign==1? INT_MAX: INT_MIN;
                }
                i++;
        }
        return sign*res;


    }
};