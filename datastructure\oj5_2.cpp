#include <iostream>
#include <vector>
#include <queue>
#include <unordered_set>
using namespace std;

bool solve() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);

    int n;
    cin >> n;
    vector<vector<int>> adjList(n + 1); // 使用邻接表来存储图
    for (int i = 0; i < n - 1; i++) {
        int x, y;
        cin >> x >> y;
        adjList[x].push_back(y);
        adjList[y].push_back(x);
    }

    vector<int> seq(n, 0);
    for (int i = 0; i < n; i++) {
        cin >> seq[i];
    }

    if (seq[0] != 1) { // BFS 序列必须以节点 1 开始
        return false;
    }

    int index = 0;
    vector<bool> vis(n + 1, false);
    queue<int> q;
    q.push(seq[index++]);
    vis[seq[0]] = true;

    while (!q.empty()) {
        int x = q.front();
        q.pop();
        unordered_set<int> s;

        for (int i : adjList[x]) { // 直接遍历邻接节点
            if (!vis[i]) {
                s.insert(i);
            }
        }

        while (!s.empty() && index < n && s.find(seq[index]) != s.end()) {
            q.push(seq[index]);
            vis[seq[index]] = true;
            s.erase(seq[index]); // 从集合中移除已访问的节点
            index++;
        }

        if (!s.empty()) { // 如果还有未访问的邻接节点，说明序列不合法
            return false;
        }
    }

    return true; // 如果所有节点都被正确访问，返回 true
}

int main() {
    bool res = solve();
    if (res) {
        cout << "Yes" << endl;
    } else {
        cout << "No" << endl;
    }
    return 0;
}