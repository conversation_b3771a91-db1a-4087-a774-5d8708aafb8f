#include<vector>
using namespace std;
class Solution {
public:
    int minCostClimbingStairs(vector<int>& cost) {
        int n=cost.size();
        int *dp=new int[n+1];
        dp[0]=0;
        dp[1]=0;
        for(int i=2;i<=n;i++)
        {
            dp[i]=min(dp[i-1]+cost[i-1],dp[i-2]+cost[i-2]);

        }
        return dp[n];


    }
};
int main()
{
    Solution sol;
    vector<int> v{1,100,1,1,1,100,1,1,100,1};
    auto res=sol.minCostClimbingStairs(v);
    return 0;
}