#include<iostream>
using namespace std;
int kanpsack(int v[],int w[],int n,int m)
{
    int dp[n+1][m+1]={};
    for(int i=1;i<=n;i++)
    {
        for(int j=1;j<=m;j++)
        {
            if(j<w[i])
            {
                dp[i][j]=dp[i-1][j];
            }
            else
            {
                dp[i][j]=max(dp[i-1][j],dp[i-1][j-w[i]]+v[i]);
            }
        }
    }
    return dp[n][m];
}
int main()
{
    int n,m;
    cin>>m>>n;
    int v[n+1]={};
    int w[n+1]={};
    for(int i=1;i<=n;i++)
    {
        cin>>w[i]>>v[i];
    }
    int res=kanpsack(v,w,n,m);
    cout<<res<<endl;
    return 0;
}