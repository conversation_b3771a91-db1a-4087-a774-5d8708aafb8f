#include<iostream>
#include<vector>
#include<set>
using namespace std;
class Solution {
public:
    vector<vector<int>> permuteUnique(vector<int>& nums) {
        len=nums.size();
        dfs(0,nums);
        return res;

    }
    void dfs(int t,vector<int> &nums)
    {
        if(t==len) res.push_back(nums);
        set<int> st;
        for(int i=t;i<len;i++)
        {
            if(st.find(nums[i])!=st.end()) continue;
            st.insert(nums[i]);
            swap(nums[i],nums[t]);
            dfs(t+1,nums);
            swap(nums[i],nums[t]);
        
        }
        
    }
private:
    int len;;
    vector<vector<int>> res;
};
int main()
{
    vector<int> v{2,2,1,1};
    Solution sol;
    auto res=sol.permuteUnique(v);
    return 0;
}