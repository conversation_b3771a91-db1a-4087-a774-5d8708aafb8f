#include<iostream>
#include<algorithm>
using namespace std;
void kanpsack(float v[],float w[],int n,float m,float x[])
{
    int index[n];
    for(int i=0;i<n;i++)
    {
        index[i]=i;
    }
    sort(index,index+n,[&](int a,int b){return index[a]<index[b];});
    for(int i=0;i<n;i++)
    {
        x[i]=0;
    }
    float c=m;
    int i;
    for(i=0;i<n;i++)
    {
        if(c<w[i]) break;
        x[i]=1;
        c-=w[i];
    }
    if(i<n)
    {
        x[i]=c/w[i];
    }

}
int main()
{
    int n;
    float weight;
    cin>>n>>weight;
    float v[n],w[n],x[n];
    for(int i=0;i<n;i++)
    {
        cin>>v[i];
    }
    for(int i=0;i<n;i++)
    {
        cin>>w[i];
    }
    kanpsack(v,w,n,weight,x);
    for(int i=0;i<n;i++)
    {
        cout<<x[i]<<endl;
    }
    return 0;

}