#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    vector<string> removeSubfolders(vector<string> &folder)
    {
        sort(folder.begin(), folder.end());
        vector<string> ans;
        ans.push_back(folder[0]);
        for (int i = 1; i < folder.size(); i++)
        {
            string &curr = folder[i];
            string &prev = ans.back();
            if (!(prev.size() < curr.size() && curr[prev.size()] == '/' && curr.substr(0, prev.size()) == prev))
            {
                ans.push_back(curr);
            }
        }
        return ans;
    }
};