#include <vector>
#include <iostream>
#include <unordered_map>
using namespace std;
class Solution
{
public:
    int minimumRounds(vector<int> &tasks)
    {
        unordered_map<int, int> m;
        for (int i = 0; i < tasks.size(); i++)
        {
            m[tasks[i]]++;
        }
        int ans = 0;
        for (auto &[_, c] : m)
        {
            if (c == 1)
            {
                return -1;
            }
            ans += (c + 2) / 3; // 这里使用c+2是因为c可能为0，这样除以3的结果为0，不符合题意，所以加2来避免这种情况。
        }
        return ans; // 返回最少需要的轮数。
    }
};