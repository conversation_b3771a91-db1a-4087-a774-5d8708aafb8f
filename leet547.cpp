#include<vector>
#include<unordered_set>
using namespace std;
class Solution
{
public:
    int findCircleNum(vector<vector<int>> &isConnected)
    {
        int n=isConnected.size();
        for(int i=0;i<n;i++)
        {
            fa.emplace_back(i);
        }
        for(int i=0;i<n;i++)
        {
            for(int j=0;j<n;j++)
            {
                if(isConnected[i][j]==1)
                {
                    merge(i,j);
                }
            }
        }
        unordered_set<int> st;
        int cnt=0;
        for(int i=0;i<n;i++)
        {
            if(st.find(find(i))==st.end())
            {
                st.insert(find(i));
                cnt++;
            }
        }
        return cnt;
    }
    int find(int x)
    {
        return x==fa[x]? x:(fa[x]=find(fa[x]));
    }
    void merge(int i,int j)
    {
        fa[find(i)]=find(j);
    }
private:
    vector<int> fa;
};
int main()
{
    Solution sol;
    vector<vector<int>> v = {{ 1, 1, 0 }, { 1, 1, 0 }, { 0, 0, 1 } };
    int res= sol.findCircleNum(v);
    return 0;;
}