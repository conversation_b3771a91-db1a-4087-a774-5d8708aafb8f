#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    int canCompleteCircuit(vector<int> &gas, vector<int> &cost)
    {
        int n = gas.size();
        int curr_fuel = 0;
        int total_fuel = 0;
        int res_index = 0;
        for (int i = 0; i < n; i++)
        {
            curr_fuel += gas[i] - cost[i];
            total_fuel += gas[i] - cost[i];
            if (curr_fuel < 0)
            {
                res_index = i + 1;
                curr_fuel = 0;
            }
        }
        return (total_fuel >= 0) ? res_index : -1;
    }
};