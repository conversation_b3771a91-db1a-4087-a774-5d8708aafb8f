#include<vector>
using namespace std;
class Solution {
public:
    vector<int> luckyNumbers (vector<vector<int>>& matrix) {
        int m=matrix.size(),n=matrix[0].size();
        vector<int> row(m);
        vector<int> col(n);
        for(int i=0;i<m;i++)
        {
            int min=matrix[i][0];
            for(int j=0;j<n;j++)
            {
                if(matrix[i][j]<min)
                {
                    min=matrix[i][j];
                }
            }
            row[i]=min;
        }
        for(int i=0;i<n;i++)
        {
            int max=matrix[0][i];
            for(int j=0;j<m;j++)
            {
                if(matrix[j][i]>max)
                {
                    max=matrix[j][i];
                }
            }
            col[i]=max;
        }
        vector<int> ans;
        for(int i=0;i<m;i++)
        {
            for(int j=0;j<n;j++)
            {
                if(row[i]==col[j] && matrix[i][j]==row[i])
                {
                    ans.push_back(row[i]);
                }
            }
        }
        return ans;

    }
};