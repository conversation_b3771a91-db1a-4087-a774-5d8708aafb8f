#include<bits/stdc++.h>
using namespace std;
class Solution {
public:
    int threeSumClosest(vector<int>& nums, int target) {
        sort(nums.begin(),nums.end());
        int ans=nums[0]+nums[1]+nums[2];
        if(ans==target) return ans;
        int len=nums.size();
        for(int i=0;i<len;i++)
        {
            int l=i+1,r=len-1;
            while(l<r)
            {
                int sum=nums[i]+nums[l]+nums[r];
                if(abs(sum-target)<abs(ans-target))
                {
                    ans=sum;
                }
               if(sum>target) r--;
               else if(sum<target) l++;
               else return ans;
            }


        }
        return ans;

    }
};