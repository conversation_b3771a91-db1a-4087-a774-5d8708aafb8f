#include<vector>
#include<iostream>
using namespace std;
class Solution {
public:
    int trap(vector<int>& height) {
        int size=height.size();
        int left=1,right=size-2;
        int lmax=0,rmax=0;
        int res=0;
        while(left<=right)
        {
            lmax=max(height[left-1],lmax);
            rmax=max(height[right+1],rmax);
            if(lmax>=rmax)
            {
                if(rmax>height[right])
                {
                    res+=rmax-height[right];
                }
                right--;
                
            }
            else
            {
                if(lmax>height[left])
                {
                    res+=lmax-height[left];
                }
                left++;
            }
        }
        return res;

    }
};
int main()
{
    vector<int> v{0,1,0,2,1,0,1,3,2,1,2,1};
    Solution sol;
    int res=sol.trap(v);
    cout<<res<<endl;
    return 0;
}