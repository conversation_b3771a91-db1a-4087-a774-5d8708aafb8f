#include<iostream>
using namespace std;
int a[10001];
int dp[10001];
int q[10001];
int main()
{
    int n=0;
    while(cin>>a[n]) n++;
    int max_sum1=0;
    int total=1;
    for(int i=0;i<n;i++)
    {
        dp[i]=1;
        for(int j=i-1;j>=0;j--)
        {
            if(a[i]<=a[j])
            {
                dp[i]=max(dp[i],dp[j]+1);
            }
        }
        max_sum1=max(max_sum1,dp[i]);
    }
    q[1]=a[0];
    for(int i=1;i<n;i++)
    {
        int k=1;
        while(a[i]>q[k] && k<=total) k++;
        if(k<=total) q[k]=a[i];
        else
        {
            q[k]=a[i];
            total++;
        }

    }
    cout<<max_sum1<<" "<<total<<endl;
    return 0;
    
}