#include <bits/stdc++.h>
using namespace std;

class Solution {
  public:
    string longestPalindrome(string s) {
        int n = s.length();
        vector<vector<bool>> dp(n, vector<bool>(n));
        int max_len = 0;
        int res_i;
        for (int l = 1; l <= n; l++) {
            for (int i = 0; i < n; i++) {
                int j = l + i - 1;
                if (s[i] != s[j]) {
                    dp[i][j] = false;
                } else {
                    dp[i][j] = l <= 2 || dp[i + 1][j - 1];
                }
                if (dp[i][j] && l > max_len) {
                    res_i = i;
                    max_len = l;
                }
            }
        }
        return s.substr(res_i, max_len);
    }
};
int main() {
    Solution solution;
    string s = solution.longestPalindrome("aacabdkacaa");
    cout << s << endl;
    return 0;
}