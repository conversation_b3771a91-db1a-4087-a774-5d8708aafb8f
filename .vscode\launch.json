{"version": "0.2.0", "configurations": [{"name": "C/C++: g++.exe 生成和调试活动文件", "type": "cppdbg", "request": "launch", "program": "E:\\code\\cpp_build\\${fileBasenameNoExtension}.exe", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "E:\\msys64\\ucrt64\\bin\\gdb.exe", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "将反汇编风格设置为 Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "preLaunchTask": "C/C++: g++.exe 生成活动文件"}]}