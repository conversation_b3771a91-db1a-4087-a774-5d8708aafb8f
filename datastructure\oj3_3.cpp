#include<iostream>
#include<vector>
using namespace std;
void find_post_order(vector<char>&pre_order, vector<char>&in_order, vector<char>& post_order, int pre_s, int pre_e, int in_s, int in_e)
{
    if(pre_s>pre_e || in_s>in_e) return;
    int root=pre_order[pre_s];
    int in_root_index=in_s;
    while(in_order[in_root_index]!=root)
    {
        in_root_index++;
    }
    int left_tree_size=in_root_index-in_s;
    find_post_order(pre_order, in_order, post_order, pre_s+1,pre_s+left_tree_size, in_s, in_root_index-1);
    find_post_order(pre_order, in_order, post_order, pre_s+left_tree_size+1, pre_e, in_root_index+1, in_e);
    post_order.push_back(root);

}

int main()
{
    int n;
    string s;
    vector<char> pre_order;
    vector<char> in_order;
    vector<char> post_order;
    while(cin>>n)
    {
        if(n==0) break;
        post_order.clear();
        pre_order.clear();
        in_order.clear();
        cin>>s;
        for(int i=0;i<n;i++)
        {
            pre_order.push_back(s[i]);
        }
        cin>>s;
        for(int i=0;i<n;i++)
        {
            in_order.push_back(s[i]);
        }
        find_post_order(pre_order, in_order, post_order, 0, n-1, 0, n-1);
        for(int i=0;i<n;i++)
        {
            cout<<post_order[i];
        }
        cout<<endl;

        

    }
    return 0;
    
}