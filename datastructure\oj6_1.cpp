#include <bits/stdc++.h>
using namespace std;
vector<int> G[400005];
bool vis[400005], tpd[400005];
int n, m, ans[400005], deg[400005];
double pth[400005];
void bfs()
{
    queue<int> Q;
    Q.push(1);
    while (!Q.empty())
    {
        int now = Q.front();
        Q.pop();
        if (vis[now])
            continue;
        vis[now] = true;
        ans[now] = 1;
        for (auto to : G[now])
            Q.push(to);
    }
}
void topSort()
{
    for (int i = 1; i <= n; ++i)
        if (vis[i])
            for (auto to : G[i])
                ++deg[to];
    queue<int> Q;
    for (int i = 1; i <= n; ++i)
        if (!deg[i] && vis[i])
            Q.push(i), pth[i] = 1;
    while (!Q.empty())
    {
        int now = Q.front();
        Q.pop();
        tpd[now] = true;
        for (auto to : G[now])
        {
            --deg[to];
            pth[to] += pth[now];
            if (!deg[to])
                Q.push(to);
        }
    }
    if (!tpd[1])
    {
        for (int i = 1; i <= n; ++i)
            if (ans[i])
                ans[i] = -1;
        return;
    }
    for (int i = 1; i <= n; ++i)
    {
        if (ans[i])
        {
            if (!tpd[i])
                ans[i] = -1;
            else if (pth[i] >= 2)
                ans[i] = 2;
        }
    }
}
int main()
{
    int T;
    scanf("%d", &T);
    while (T-- > 0)
    {
        scanf("%d %d", &n, &m);
        for (int i = 1; i <= n; ++i)
            pth[i] = ans[i] = 0, vis[i] = tpd[i] = false, G[i].clear(), deg[i] = 0;
        for (int i = 1; i <= m; ++i)
        {
            int u, v;
            scanf("%d %d", &u, &v);
            G[u].push_back(v);
        }
        bfs();
        topSort();
        for (int i = 1; i <= n; ++i)
            printf("%d ", ans[i]);
        puts("");
    }
    return 0;
}