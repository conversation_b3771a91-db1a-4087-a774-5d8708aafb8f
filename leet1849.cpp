#include <bits/stdc++.h>
using namespace std;

class Solution
{
public:
    bool splitString(string s)
    {
        return dfs(s, 0, "", 0);
    }

private:
    bool dfs(const string &s, int start, string prev, int count)
    {
        if (start == s.size())
            return count >= 2;

        string num = "";
        for (int i = start; i < s.size(); ++i)
        {
            num += s[i];

            if (prev.empty() || isPrevMinusOne(prev, num))
            {
                if (dfs(s, i + 1, num, count + 1))
                    return true;
            }
            else if (!prev.empty() && compare(num, prev) >= 0)
            {
                break;
            }
        }
        return false;
    }

    bool isPrevMinusOne(const string &prev, const string &num)
    {
        string p = removeLeadingZeros(prev);
        string n = removeLeadingZeros(num);
        if (p == "" || n == "")
            return false;
        string pMinusOne = subtractOne(p);
        return pMinusOne == n;
    }

    string subtractOne(string num)
    {
        int i = num.size() - 1;
        while (i >= 0 && num[i] == '0')
        {
            num[i] = '9';
            i--;
        }
        if (i >= 0)
        {
            num[i]--;
            if (num[0] == '0' && num.size() > 1)
                num.erase(0, 1); // 去除前导零
        }
        else
        {
            return ""; // 无效情况
        }
        return num;
    }

    string removeLeadingZeros(const string &num)
    {
        int i = 0;
        while (i < num.size() && num[i] == '0')
            i++;
        return i == num.size() ? "0" : num.substr(i);
    }

    int compare(const string &a, const string &b)
    {
        string sa = removeLeadingZeros(a);
        string sb = removeLeadingZeros(b);
        if (sa.size() != sb.size())
            return sa.size() - sb.size();
        return sa.compare(sb);
    }
};

int main()
{
    Solution sol;
    string s = "050043";
    cout << (sol.splitString(s) ? "True" : "False") << endl;
    return 0;
}