#include<iostream>
using namespace std;

// 动态规划计算贝尔数
int bellNumber(int n) {
    int bell[n+1][n+1];
    bell[0][0] = 1;

    for (int i=1; i<=n; i++) {
        // 初始化行的第一个数为上一行的最后一个数
        bell[i][0] = bell[i-1][i-1];

        for (int j=1; j<=i; j++) {
            // 计算贝尔三角形的其他数值
            bell[i][j] = bell[i-1][j-1] + bell[i][j-1];
        }
    }
    return bell[n][0]; // 返回第n个贝尔数
}

int main() {
    int n;
    cout << "请输入一个整数n来计算第n个贝尔数: ";
    cin >> n;
    cout << "第" << n << "个贝尔数是: " << bellNumber(n) << endl;
    return 0;
}
