#include<vector>
#include<iostream>
using namespace std;
int cnt;
int n,m;
vector<int> vis;
 void dfs(int x,int cat_n,vector<int>& cat,vector<vector<int>>& adjlist)
    {
        if(cat[x]==1)
        {
            cat_n+=1;
            if(cat_n>m)
            {
                return;
            }
        }
        if(cat[x]==0)
        {
            cat_n=0;
        }
        vis[x]=1;
        bool is_leaf=false;
        for(int i=0;i<adjlist[x].size();i++)
        {
            int y=adjlist[x][i];
            if(vis[y]==false)
            {
                is_leaf=true;
                dfs(y,cat_n,cat,adjlist);
            }
        }
        if(is_leaf==false)
        {
            cnt++;
        }
        vis[x]=0;
        return;


    }
int main()
{
    ios::sync_with_stdio(false);
    cin.tie(nullptr);
    cin>>n>>m;
    vector<int> cat(n+1);
    vis.resize(n+1,0);
    for(int i=1;i<=n;i++)
    {
        cin>>cat[i];
    }
    vector<vector<int>> adjlist(n+1);
    for(int i=0;i<n-1;i++)
    {
        int x,y;
        cin>>x>>y;
        adjlist[x].push_back(y);
        adjlist[y].push_back(x);
    }
    dfs(1,0,cat,adjlist);
    cout<<cnt<<endl;
    return 0;
    

}