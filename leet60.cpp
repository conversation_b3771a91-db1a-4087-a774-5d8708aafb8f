#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    string getPermutation(int n, int k)
    {
        string res = "";
        vector<int> fact(n, 1);
        vector<int> nums(n, 1);
        for (int i = 1; i < n; i++)
        {
            fact[i] = fact[i - 1] * i;
            nums[i] = i + 1;
        }
        k--;
        for (int i = n - 1; i >= 0; i--)
        {
            int j = k / fact[i];
            k %= fact[i];
            res += to_string(nums[j]);
            nums.erase(nums.begin() + j);
        }
        return res;
    }
};
int main()
{
    Solution sol;
    int n = 4, k = 9;
    cout << sol.getPermutation(n, k) << endl;
    return 0;
}