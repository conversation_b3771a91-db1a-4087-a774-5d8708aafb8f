#include<bits/stdc++.h>
using namespace std;
using LL = long long;
#define ALL(v) v.begin(), v.end()
#define ALLR(v) v.rbegin(), v.rend()
#define REP(i, n) for (LL i = 0; i < n; ++i)
#define REPR(i, n) for (LL i = n; i >= 0; --i)
#define FOR(i, m, n) for (LL i = m; i < n; ++i)
#define FORR(i, m, n) for (LL i = m; i >= n; --i)
#define LOWBIT(x) ((x) & (-x))
#define MID(x, y) (((x) & y) + ((x ^ y) >> 1))
#define Print(arr, _len) do { printf("%d\n", _len); REP(_i, _len) printf(_i ? " %d" : "%d", arr[_i]); printf("\n"); } while(0);
#define PrintLL(arr, len) do { printf("%d\n", len); REP(_i, _len) printf(_i ? " %lld" : "%lld", arr[_i]); printf("\n"); } while(0);
#define PRINT(LS) do { cout << LS.size() << ":"; for (auto i : LS) cout << " " << i; cout << endl; } while(0);
#define pb push_back
#define PI (acos(-1))

constexpr auto EPS = 1E-8;
constexpr auto MOD = 1000000007LL;
constexpr auto INF = 0x3f3f3f3f;

int FastPow(int x, int y) { int ans = 1; for (long long k = x; y; y >>= 1, k = k * k % MOD) if (y & 1) ans = ans * k % MOD; return ans; }
int GetRoot(int* f, int x) { return f[x] < 0 ? x : f[x] = GetRoot(f, f[x]); }
void Merge(int* f, int x, int y) { int rx = GetRoot(f, x), ry = GetRoot(f, y); if (rx == ry) return; f[rx] += f[ry]; f[ry] = rx; }

int __FAST_IO__ = []() { std::ios::sync_with_stdio(0); std::cin.tie(0); std::cout.tie(0); return 0; }();

int f[301], g[301];

class Solution {
public:
    int minMalwareSpread(vector<vector<int>>& graph, vector<int>& initial) {
        memset(f, -1, sizeof f);
        memset(g, 0, sizeof g);
        int n = graph.size();
        REP(i, n) REP(j, n) if (graph[i][j]) Merge(f, i, j);
        for (auto& i : initial) g[GetRoot(f, i)]++;

        int x = initial.front(), y = 0;
        for (auto& i : initial) {
            auto idx = GetRoot(f, i);
            if (g[idx] > 1 && !y) {
                x = min(i, x);
            } else if (g[idx] == 1) {
                if (f[idx] < y) {
                    y = f[idx]; x = i;
                } else if (f[idx] == y) {
                    x = min(i, x);
                }
            }
        }
        return x;
    }
};