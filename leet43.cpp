#include<string>
#include<sstream>
#include<iostream>
using namespace std;
class Solution {
public:
    string multiply(string num1, string num2) {
        stringstream ss;
        long long n1,n2;
        ss<<num1<<" "<<num2;
        ss>>n1>>n2;
        long long n3=n1*n2;
        ss.clear();
        ss<<n3;
        string s;
        ss>>s;
        return s;



        


    }
};
int main()
{
    Solution sol;
    string s1="498828660196";
    string s2="840477629533";
    string res=sol.multiply(s1,s2);
    cout<<res<<endl;
    return 0;
}