#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    long long minimumTime(vector<int> &time, int totalTrips)
    {
        long long left = 1;
        long long right = (long long)*min_element(time.begin(), time.end()) * totalTrips;
        while (left < right)
        {
            long long mid = left + (right - left) / 2;
            if (ispossible(mid, time, totalTrips))
            {
                right = mid;
            }
            else
            {
                left = mid + 1;
            }
        }
        return left;
    }
    bool ispossible(long long t, vector<int> &time, int totalTrips)
    {
        long long total = 0;
        for (int &i : time)
        {
            total += t / i;
            if (total >= totalTrips)
            {
                return true;
            }
        }
        return total >= totalTrips;
    }
};