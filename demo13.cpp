#include <bits/stdc++.h>
#include <mutex> // 包含 mutex 头文件
#include <semaphore>
std::counting_semaphore<5> sem(5); // 最多5个并发线程
// std::mutex cout_mutex;             // 用于保护 std::cout 的互斥锁

void limited_task(int id) {
    sem.acquire();

    printf("Task %d is running\n", id);

    // 模拟工作
    std::this_thread::sleep_for(std::chrono::seconds(1));

    printf("Task %d is done\n", id);

    sem.release();
}

int main() {
    // setbuf(stdout, NULL); // 在使用 mutex 后，这行通常不是必需的，但保留也无妨
    std::vector<std::thread> threads;
    for (int i = 0; i < 10; ++i) {
        threads.emplace_back(limited_task, i);
    }
    for (auto &t : threads)
        t.join();
    return 0;
}
