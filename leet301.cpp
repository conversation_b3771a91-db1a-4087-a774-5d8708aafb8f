#include<queue>
#include<unordered_map>
using namespace std;
class Solution
{
public:
    vector<int> findMinHeightTrees(int n, vector<vector<int>> &edges)
    {
        vector<int> res;
        if(n == 1) return {0};
        if(n == 2) return {0,1};
        vector<int> degree(n, 0);
        unordered_map<int, vector<int>> adj;
        for(auto &edge : edges)
        {
            degree[edge[0]]++;
            degree[edge[1]]++;
            adj[edge[0]].push_back(edge[1]);
            adj[edge[1]].push_back(edge[0]);
        }
        queue<int> q;
        for (int i = 0; i < n; i++)
        {
            if (degree[i] == 1)
                q.push(i);
        }
        while(!q.empty())
        {
            int size = q.size();
            res.clear();
            for(int i = 0; i < size; i++)
            {
                int node = q.front();
                q.pop();
                res.push_back(node);
                for(auto &adjNode : adj[node])
                {
                    degree[adjNode]--;
                    if(degree[adjNode] == 1)
                    {
                        q.push(adjNode);
                    }
                }
            }
        }

        return res;
    }
   

};