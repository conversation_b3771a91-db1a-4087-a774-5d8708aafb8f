#include <bits/stdc++.h>
using namespace std;
int main()
{
    ios::sync_with_stdio(false);
    cin.tie(0);
    cout.tie(0);
    int t;
    cin >> t;
    while (t--)
    {
        int n;
        cin >> n;
        vector<int> a(n + 1);
        for (int i = 1; i <= n; i++)
        {
            cin >> a[i];
        }
        int max = 0;
        unordered_map<int, int> dp;
        for (int i = n; i >= 1; i--)
        {
            if (i + a[i] > n)
            {
                dp[i] = a[i];
            }
            else
            {
                dp[i] = a[i] + dp[i + a[i]];
            }
            max = dp[i] > max ? dp[i] : max;
        }
        cout << max << endl;
    }
    return 0;
}