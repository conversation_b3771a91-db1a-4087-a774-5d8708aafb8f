#include "Polynomial.h"
#include<iostream>
using namespace std;
int main()
{
    int n;
    cin>>n;
    Polynomial p1;
    Polynomial p2;
    Polynomial result;
    int x,y;
    for(int i=0;i<n;i++)
    {
        cin>>x>>y;
        p1.addterm(x,y);
    }
    cin>>n;
    for(int i=0;i<n;i++)
    {
        cin>>x>>y;
        p2.addterm(x,y);
    }
    result=p1+p2;
    for(int i=0;i<result.getsize();i++)
    {
        cout<<result.terms[i].first<<" "<<result.terms[i].second<<endl;
    }
    return 0;

}