#include <iostream>
#include <coroutine>
#include <future>

struct task_promise;
struct task
{
    using promise_type = task_promise;
    std::coroutine_handle<promise_type> coro;

    task(std::coroutine_handle<promise_type> h) : coro(h) {}
    ~task()
    {
        if (coro)
            coro.destroy();
    }

    void get() { coro.resume(); }
};

struct task_promise
{
    auto get_return_object() { return task{std::coroutine_handle<task_promise>::from_promise(*this)}; }
    std::suspend_always initial_suspend() { return {}; }
    std::suspend_always final_suspend() noexcept { return {}; }
    void return_void() {}
    void unhandled_exception() { std::terminate(); }
};

task async_function()
{
    std::cout << "Start async work" << std::endl;
    co_await std::suspend_always{};
    std::cout << "Resume async work" << std::endl;
}

int main()
{
    auto f = async_function();
    std::cout << "Doing other work..." << std::endl;
    f.get();
    return 0;
}
