#include <vector>
#include <string>
#include<queue>
#include <unordered_set>
using namespace std;
class Solution {
public:
    vector<vector<string>> findLadders(string beginWord, string endWord, vector<string>& wordList) {

    }
private:
     void find(string endword, unordered_set<string> words, vector<string>& wordlist, vector<string> path)
     {
        string cur=path.back();
        for(string s:words)
        {
            if(isvalid(cur,s))
            {
                path.push_back(s);
                if(s==endword)
                {
                    falg=true;
                    
                }

            }
        }
     }
    bool isvalid(string word, string endword)
    {
        int n = word.size();
        int m = endword.size();
        if(n!=m) return false;
        int diff = 0;
        for(int i=0;i<n;++i)
        {
            if(word[i]!=endword[i])
            diff++;
            if(diff>1) return false;
        }
        return true;
    }
    bool falg=false;
    

};