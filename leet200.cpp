#include <vector>
using namespace std;
class Solution {
public:
    int numIslands(vector<vector<char>>& grid) {
        m=grid.size();
        if(m==0) return 0;
        n=grid[0].size();
        vis=vector<vector<bool>>(m,vector<bool>(n,false));
        int ans=0;
        for(int i=0;i<m;++i)
        {
            for(int j=0;j<n;++j)
            {
                if(grid[i][j]=='1'&&!vis[i][j])
                {
                    ++ans;
                    bfs(grid,i,j);
                }
            }
        }
        return ans;

        
    }
private:
    int m;
    int n;
    vector<int> dx={0,1,0,-1};
    vector<int> dy={1,0,-1,0};
    vector<vector<bool>> vis;
    void bfs(vector<vector<char>>& grid,int x,int y)
    {
        if(x<0||y<0||x>=m||y>=n) return;
        if(vis[x][y]||grid[x][y]=='0') return;
        vis[x][y]=true;
        for(int i=0;i<4;++i)
        {
            bfs(grid,x+dx[i],y+dy[i]);
        }
    }
};