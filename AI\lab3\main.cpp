
#include <cstdio>
#ifdef ANS
#include "usr-ans.h"
#else
#include "usr.h"
#endif

int main()
{
	char c = 0;
	//printf("\n\t\t******************************** ÒÅ´«Ëã·¨Çó½âTSP(ÂÃÐÐÉÌ)ÎÊÌâ *********************************\n");
	initGroup();	//³õÊ¼»¯
	popFitness();	//¸üÐÂÊý¾Ý
	//Êä³öÅäÖÃÐÅÏ¢
	/*printf("\n\t\t»ùÒò³¤¶È:%d",cityNum);
	printf("\tÖÖÈº´óÐ¡:%d",popSize);
	printf("\t½»²æ¸ÅÂÊ:%.2f",croRate);
	printf("\t±äÒì¸ÅÂÊ:%.2f",mutRate);
	printf("\t½ø»¯´úÊý:%d",MAX);
	printf("\tÔ¤Éè×îÓÅ½â£º18");
	printf("\n\n\t\t**********************************************************************************************\n");*/

     int i,j;
	//Êä³ö¾àÀë¾ØÕó
	/*printf("\n\t\t--------- ³ÇÊÐ¾àÀë¾ØÕó ---------\n");
	printf("\t\t");
	int i,j;
	for(i = 0; i < cityNum; i ++)
	{
		for(j = 0;j < cityNum; j ++)
		{
			printf("  %d",distance[i][j]);
		}
		printf("\n\t\t");
	}
	printf("--------------------------------");*/

	//Êä³öÂ·¾¶ÐÅÏ¢
	/*printf("\n\t\t-------- ³õÊ¼ÖÖÈº»ùÒò¿â --------\n");
	printf("\t\t ");

	for(i = 0; i < cityNum; i ++)
	{
		printf("  %c",genes[i].name);
	}
	printf("\n\t\t");
	for(i = 0;i < cityNum; i ++)
	{
		printf("%c",genes[i].name);
		for(j = 0; j < cityNum; j ++)
		{
			printf("  %d",genes[i].cityArr[j]);
		}
		printf("\n\t\t");
	}
	printf("--------------------------------\n");*/


		//printf("\n\t\tÑ°Çó×îÓÅ½âÖÐ£º");
		//Í¨¹ý²»¶Ï½ø»¯£¬Ö±µ½´ïµ½¶¨ÒåµÄ½ø»¯´úÊý
		for(i = 0; i < MAX; i ++)
		{
			select();
			cross();
			mutation();
			popFitness();//¸üÐÂÊý¾Ý
			int temp = (int)MAX/20;
			/*if( i % temp == 0)
			{
				//printf("¨");
				Sleep(200);
			}*/
		}
		//printf("Íê³É");
		//printf("\n\n\t\t×îÓÅÂ·¾¶£º");
		/*for(i = 0; i < cityNum ; i++)
		{
			printf("%d-->",genes[chooseBest()].cityArr[i]);
		}
		printf("%d",genes[chooseBest()].cityArr[0]);*/
		printf("%d",genes[chooseBest()].dis);
		//printf("\n\n\t\tÊÇ·ñÔÙÊÔÒ»´Î?(Y/y) ÊÇ/(N/n) ·ñ£º");
	    fflush(stdin);
	   /* c = getchar();
	    fflush(stdin);
		if(c=='N'||c=='n')
		{
			break;
		}*/

	//printf("\n\t\t");
	return 0;
}
