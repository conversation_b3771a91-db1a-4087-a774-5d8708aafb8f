{"files.associations": {"iostream": "cpp", "vector": "cpp", "iosfwd": "cpp", "set": "cpp", "ostream": "cpp", "algorithm": "cpp", "queue": "cpp", "deque": "cpp", "array": "cpp", "any": "cpp", "string_view": "cpp", "ranges": "cpp", "span": "cpp", "stacktrace": "cpp", "regex": "cpp", "unordered_map": "cpp", "bitset": "cpp", "initializer_list": "cpp", "stdatomic.h": "c", "atomic": "cpp", "thread.h": "c", "stdlib.h": "c", "unistd.h": "c", "barrier": "cpp", "bit": "cpp", "*.tcc": "cpp", "cctype": "cpp", "cfenv": "cpp", "charconv": "cpp", "chrono": "cpp", "cinttypes": "cpp", "clocale": "cpp", "cmath": "cpp", "codecvt": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "condition_variable": "cpp", "coroutine": "cpp", "csetjmp": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cuchar": "cpp", "cwchar": "cpp", "cwctype": "cpp", "forward_list": "cpp", "list": "cpp", "map": "cpp", "string": "cpp", "unordered_set": "cpp", "exception": "cpp", "expected": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "source_location": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "format": "cpp", "fstream": "cpp", "future": "cpp", "iomanip": "cpp", "istream": "cpp", "latch": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "numbers": "cpp", "scoped_allocator": "cpp", "semaphore": "cpp", "shared_mutex": "cpp", "spanstream": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stdfloat": "cpp", "stop_token": "cpp", "streambuf": "cpp", "syncstream": "cpp", "thread": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "valarray": "cpp", "variant": "cpp", "stdio.h": "c", "time.h": "c", "stack": "cpp", "limits.h": "c", "generator": "cpp", "text_encoding": "cpp", "print": "cpp", "filesystem": "cpp", "flat_map": "cpp", "flat_set": "cpp"}, "C_Cpp.default.compilerPath": "E:\\msys64\\ucrt64\\bin\\g++.exe", "Codegeex.CompletionModel": "CodeGeeX Lite", "editor.formatOnPaste": true, "editor.formatOnSave": true, "editor.formatOnType": true, "Codegeex.RepoIndex": true, "editor.tabOut": true, "editor.tabCompletion": "on"}