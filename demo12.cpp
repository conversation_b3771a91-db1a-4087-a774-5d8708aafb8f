#include <condition_variable>
#include <iostream>
#include <mutex>
#include <queue>
#include <thread>

std::mutex mtx;
std::condition_variable cv;
std::queue<int> data_queue;
const int MAX_SIZE = 10;

void producer() {
    for (int i = 0; i < 20; ++i) {
        // std::this_thread::sleep_for(std::chrono::milliseconds(100));

        std::unique_lock<std::mutex> lock(mtx);
        // 如果队列已满，等待消费者消费
        cv.wait(lock, [] { return data_queue.size() < MAX_SIZE; });

        data_queue.push(i);
        std::cout << "Produced: " << i << std::endl;

        lock.unlock();
        cv.notify_one(); // 通知消费者
    }
}

void consumer() {
    while (true) {
        std::unique_lock<std::mutex> lock(mtx);
        // 如果队列为空，等待生产者生产
        cv.wait(lock, [] { return !data_queue.empty(); });

        int data = data_queue.front();
        data_queue.pop();
        std::cout << "Consumed: " << data << std::endl;

        lock.unlock();
        cv.notify_one(); // 通知生产者

        if (data == 19)
            break; // 结束条件
    }
}

int main() {
    std::thread t1(producer);
    std::thread t2(consumer);

    t1.join();
    t2.join();

    return 0;
}