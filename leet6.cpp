#include<bits/stdc++.h>
using namespace std;
class Solution {
public:
    string convert(string s, int numRows) {
        vector<string>ss;
        vector<int> arr;
        for(int i=0;i<numRows;i++) arr.push_back(i);
        for(int i=numRows-2;i>0;i--) arr.push_back(i);
        int arr_size=arr.size();
        for(int i=0;i<numRows;i++)
        {
            ss.push_back("");
        }
        int n=s.length();
        string res;
        for(int i=0;i<n;i++)
        {
            ss[arr[i%arr_size]]+=s[i];
        }
        for(int i=0;i<numRows;i++)
        {
            res+=ss[i];
        }
        return res;
        


    }
};
int main()
{
    Solution sol;
    string s= sol.convert("A",1);
    cout<<s<<endl;
    return 0;
}