#include <bits/stdc++.h>
using namespace std;
class Solution {
  public:
    vector<int> fairCandySwap(vector<int> &aliceSizes, vector<int> &bobSizes) {
        int sum1 = 0, sum2 = 0;
        for (int i = 0; i < aliceSizes.size(); i++)
            sum1 += aliceSizes[i];
        for (int i = 0; i < bobSizes.size(); i++)
            sum2 += bobSizes[i];
        int diff = (sum1 - sum2) / 2;
        unordered_set<int> s;
        for (int i = 0; i < aliceSizes.size(); i++)
            s.insert(aliceSizes[i]);
        for (int i = 0; i < bobSizes.size(); i++) {
            if (s.find(bobSizes[i] + diff) != s.end())
                return {bobSizes[i] + diff, bobSizes[i]};
        }
        return {};
    }
};