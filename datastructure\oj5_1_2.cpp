#include <iostream>
#include <vector>
#include <queue>
#include <algorithm>
#include <climits>
typedef long long ll;
using namespace std;
const int dir[4][2] = {{1, 0}, {-1, 0}, {0, 1}, {0, -1}};
int n, m, w;
vector<vector<ll>> grid;
vector<vector<ll>> dist1;
vector<vector<ll>> dist2;
bool isvalid(int x, int y)
{
    return x >= 0 && y >= 0 && x < n && y < m && grid[x][y] != -1;
}

void bfs(int sx, int sy, vector<vector<ll>> &dist)
{
    dist.assign(n, vector<ll>(m, -1));
    queue<pair<int, int>> q;
    q.push({sx, sy});
    dist[sx][sy] = 0;
    while (!q.empty())
    {
        auto [x, y] = q.front();
        q.pop();
        for (int i = 0; i < 4; i++)
        {
            int nx = x + dir[i][0];
            int ny = y + dir[i][1];
            if (isvalid(nx, ny) && dist[nx][ny] == -1)
            {
                dist[nx][ny] = dist[x][y] + 1;
                q.push({nx, ny});
            }
        }
    }
}
int main()
{
    ios::sync_with_stdio(false), cin.tie(nullptr);
    cin >> n >> m >> w;
    grid.assign(n, vector<ll>(m, 0));
    for (int i = 0; i < n; i++)
    {
        for (int j = 0; j < m; j++)
        {
            cin >> grid[i][j];
        }
    }
    bfs(0, 0, dist1);
    bfs(n - 1, m - 1, dist2);
    ll best_ed = LLONG_MAX;
    for (int i = 0; i < n; i++)
    {
        for (int j = 0; j < m; j++)
        {
            if (dist2[i][j] != -1 && grid[i][j] > 0)
            {
                best_ed = min(best_ed, dist2[i][j] * w * 1ll + grid[i][j]);
            }
        }
    }
    ll res = dist1[n - 1][m - 1] * w * 1ll;
    if (dist1[n-1][m-1] == -1)
        res = LLONG_MAX;
    for (int i = 0; i < n; i++)
    {
        for (int j = 0; j < m; j++)
        {
            if (dist1[i][j] != -1 && grid[i][j] > 0 && best_ed != LLONG_MAX)
            {
                res = min(res, dist1[i][j] * w * 1ll + grid[i][j] + best_ed);
            }
        }
    }
    if (res == LLONG_MAX)
    {
        cout << -1 << endl;
    }
    else
    {
        cout << res << endl;
    }
    return 0;
}