#include <pthread.h>
#include <stdio.h>
#include <unistd.h>

int *other_thread_stack_var = NULL;

void *thread_func(void *arg) {
    int local_var = 42;
    other_thread_stack_var = &local_var; // 将栈变量地址暴露给全局变量
    sleep(3);
    printf("线程栈变量: %d\n", local_var); // 输出42
    return NULL;
}

int main() {
    pthread_t tid;
    pthread_create(&tid, NULL, thread_func, NULL);
    sleep(1); // 等待线程运行

    if (other_thread_stack_var != NULL) {
        printf("偷看另一个线程的栈: %d\n", *other_thread_stack_var); // 输出42
        *other_thread_stack_var = 100;                               // 修改另一个线程的栈！
    }
    pthread_join(tid, NULL);
    return 0;
}