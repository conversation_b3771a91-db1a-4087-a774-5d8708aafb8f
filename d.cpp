#include <iostream>
#include <unordered_map>

int main()
{
    std::unordered_map<int, std::string> myMap;

    // 插入一些元素
    myMap[1] = "one";
    myMap[2] = "two";
    myMap[3] = "three";

    // 计算哈希值并确定桶索引
    auto hashFunction = myMap.hash_function();
    size_t bucketCount = myMap.bucket_count();

    for (const auto &pair : myMap)
    {
        int key = pair.first;
        std::size_t hashValue = hashFunction(key);
        std::size_t bucketIndex = hashValue % bucketCount;

        std::cout << "Key: " << key << ", Hash Value: " << hashValue
                  << ", Bucket Index: " << bucketIndex << std::endl;
    }

    return 0;
}