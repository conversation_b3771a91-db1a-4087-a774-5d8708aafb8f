#include "stdio.h"
#include "stdlib.h"
#include "time.h"
#define cityNum 10				
#define popSize 10
#define croRate 0.85				
#define mutRate 0.1				
#define MAX 999					

//定义染色体的结构
struct Chrom
{
	int cityArr[cityNum];		
	char name;				
	float adapt;				
	int dis;					
};
struct Chrom genes[popSize];	
struct Chrom genesNew[popSize]; 
struct Chrom temp;			


char names[cityNum] = {'A','B','C','D','E','F','G','H','I','J'};		

int distance[cityNum][cityNum] = {{ 0, 1, 2, 3, 4, 5, 6, 7, 8, 9  },	  
							 {  1, 0, 1, 2, 3, 4, 5, 6, 7, 8  },
							 {  2, 1, 0, 1, 2, 3, 4, 5, 6, 7  },
							 {  3, 2, 1, 0, 1, 2, 3, 4, 5, 6  },
							 {  4, 3, 2, 1, 0, 1, 2, 3, 4, 5  },
							 {  5, 4, 3, 2, 1, 0, 1, 2, 3, 4  },
							 {  6, 5, 4, 3, 2, 1, 0, 1, 2, 3  },
							 {  7, 6, 5, 4, 3, 2, 1, 0, 1, 2  },
							 {  8, 7, 6, 5, 4, 3, 2, 1, 0, 1  },
							 {  9, 8, 7, 6, 5, 4, 3, 2, 1, 0  }};	

void initGroup()
{
	int i,j,k;
	int t = 0;
	int flag = 0;
	srand(time(NULL));
	for(i = 0; i < popSize; i ++)
	{
		
	    temp.name = names[i];
		temp.adapt = 0.0f;
		temp.dis = 0;
		
		for(j = 0; j < cityNum;)
		{
			t = rand()%cityNum;	
			flag = 1;
			for(k = 0; k < j; k ++)
			{
				if(genes[i].cityArr[k] == t)
				{
					flag = 0;
					break;
				}
			}
			if(flag)
			{
				temp.cityArr[j] = t;
				genes[i] = temp;
				j++;
			}
		}
	}
}

void popFitness()
{
	int i,n1,n2;
	for(i = 0; i < popSize; i ++)
	{
		genes[i].dis = 0;
		for(int j = 1;j < cityNum; j ++)
		{
			n1 = genes[i].cityArr[j-1];
			n2 = genes[i].cityArr[j];
			genes[i].dis += distance[n1][n2];
		}
		genes[i].dis += distance[genes[i].cityArr[0]][genes[i].cityArr[cityNum-1]];
		genes[i].adapt = (float)1/genes[i].dis;	
	}
}

int chooseBest()
{
	int choose = 0;
	float best = 0.0f;
	best = genes[0].adapt;
	for(int i = 0; i < popSize; i ++)
	{
		if(genes[i].adapt < best)
		{
			best = genes[i].adapt;
			choose = i;
		}
	}
	return choose;
}

void select()
{
	float biggestSum = 0.0f;
	float adapt_pro[popSize];
	float pick = 0.0f;
	int i;
	for(i = 0; i < popSize; i ++)
	{
		 biggestSum += genes[i].adapt; 
	}
	for(i = 0; i < popSize; i ++)
	{
		 adapt_pro[i] = genes[i].adapt / biggestSum; 
	}
	
    for(i = 0;i < popSize; i ++)
    {
        pick = (float)rand()/RAND_MAX; 
	    /********** Begin **********/
        float sum=0;
        for(int j=0;j<popSize;j++)
        {
            sum+=adapt_pro[j];
            if(sum>=pick)
            {
                genesNew[i]=genes[j];
                break;
            }
        }
        
        
	    /********** End **********/
    }
    for(i = 0;i < popSize; i++)
    {
	    genes[i] = genesNew[i];
    }
}

void cross()
{
    float pick;
    int choice1,choice2;
    int pos1,pos2;
    int temp;
    int conflict1[popSize];	
    int conflict2[popSize];
    int num1;
    int num2;
    int index1,index2;
    int move = 0;				
    while(move < popSize-1)
    {
        pick = (float)rand()/RAND_MAX; 
        if(pick > croRate)		
        {
            move += 2;
            continue;			
        }
        choice1 = move;			
        choice2 = move+1;		
        pos1 = rand()%popSize;
        pos2 = rand()%popSize;
        while(pos1 > popSize -2 || pos1 < 1)
        {
            pos1 = rand()%popSize;
        }
        while(pos2 > popSize -2 || pos2 < 1)
        {
            pos2 = rand()%popSize;
        }

        if(pos1 > pos2)
        {
            temp = pos1;
            pos1 = pos2;
            pos2 = temp; 
        }

        for(int j = pos1;j <= pos2; j++)
        {
            temp = genes[choice1].cityArr[j];
            genes[choice1].cityArr[j] = genes[choice2].cityArr[j];
            genes[choice2].cityArr[j] = temp;
        }

        num1 = 0;
        num2 = 0;

        if(pos1 > 0 && pos2 < popSize - 1)
        {
            /********** Begin **********/
            for(int j=0;j<pos1;j++)
            {
                for(int k=pos1;k<=pos2;k++)
                {
                    if(genes[choice1].cityArr[j]==genes[choice1].cityArr[k])
                    {
                        conflict1[num1++]=j;
                    }
                    if(genes[choice2].cityArr[j]==genes[choice2].cityArr[k])
                    {
                        conflict2[num2++]=j;
                    }
                }
            }
            
            
	        /********** End **********/
            

            for(int j = pos2 + 1;j < popSize;j++)
            {
                for(int k = pos1; k <= pos2; k ++)
                {
                    /********** Begin **********/
                    if(genes[choice1].cityArr[j]==genes[choice1].cityArr[k])
                    {
                        conflict1[num1++]=j;
                    }
                    if(genes[choice2].cityArr[j]==genes[choice2].cityArr[k])
                    {
                        conflict2[num2++]=j;
                    }
                    
                    
                    /********** End **********/
                }
            }
        }
        if((num1 == num2) && num1 > 0)
        {
            for(int j = 0;j < num1; j ++)
            {
                index1 = conflict1[j];
                index2 = conflict2[j];
                temp = genes[choice1].cityArr[index1]; 
                genes[choice1].cityArr[index1] = genes[choice2].cityArr[index2];
                genes[choice2].cityArr[index2] = temp;
            }
        }
        move += 2;
    }
}

void mutation()
{
	double pick;
    int pos1,pos2,temp;
    for(int i = 0;i < popSize; i ++)
    {
        pick = (float)rand()/RAND_MAX; 
        if(pick > mutRate)
		{
            continue;
		}
        pos1 = rand()%popSize;
        pos2 = rand()%popSize;
        while(pos1 > popSize - 1)
        {
           pos1 = rand()%popSize;
        }
        while(pos2 > popSize - 1)
        {
           pos2 = rand()%popSize;
        }

	   int a = genes[i].dis;
        temp = genes[i].cityArr[pos1];
        genes[i].cityArr[pos1] = genes[i].cityArr[pos2];
        genes[i].cityArr[pos2] = temp;

		popFitness();
		if(genes[i].dis > a)
		{
			temp = genes[i].cityArr[pos1];
			genes[i].cityArr[pos1] = genes[i].cityArr[pos2];
			genes[i].cityArr[pos2] = temp;
		}
    }
}