#include<queue>
#include<vector>
#include<unordered_map>
using namespace std;
class Node {
public:
    int val;
    vector<Node*> neighbors;
    Node() {
        val = 0;
        neighbors = vector<Node*>();
    }
    Node(int _val) {
        val = _val;
        neighbors = vector<Node*>();
    }
    Node(int _val, vector<Node*> _neighbors) {
        val = _val;
        neighbors = _neighbors;
    }
};


class Solution {
public:
    Node* cloneGraph(Node* node) {
        queue<Node*> q;
        unordered_map<Node*, Node*> m;
        if (node == nullptr) return nullptr;
        Node* new_node = new Node(node->val);
        m[node] = new_node;
        q.push(node);
        while (!q.empty()) {
            Node* cur = q.front();
            q.pop();
            for (Node* n : cur->neighbors) {
                if (m.find(n) == m.end())
                {
                    Node* new_n = new Node(n->val);
                    m[n] = new_n;
                    q.push(n);
                }
                m[cur]->neighbors.push_back(m[n]);

            }
        }

    return new_node;
    }
};