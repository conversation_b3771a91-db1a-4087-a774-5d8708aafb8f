#include<bits/stdc++.h>
using namespace std;
class state
{
public:
    int x;
    int y;
    state(int x, int y) : x{x}, y{y} {};
    bool operator==(const state &other) const
    {
        return x == other.x && y == other.y;
    }
};
struct state_hash
{
    size_t operator()(const state &s) const
    {
        return hash<int>()(s.x) ^  hash<int>()(s.y);
    }
};
struct state_equal
{
    bool operator()(const state &s1, const state &s2) const
    {
        return s1 == s2;
    }
};
class Solution
{
public:
    bool canMeasureWater(int jug1Capacity, int jug2Capacity, int targetCapacity)
    {
        x=jug1Capacity;
        y=jug2Capacity;
        target=targetCapacity;
        if(target==0) return true;
        if(x+y<target) return false;
        unordered_set<state,state_hash,state_equal> st;
        queue<state> q;
        state init=state(0,0);
        q.push(init);
        st.insert(init);
        while(!q.empty())
        {
            int size=q.size();
            for(int i=0;i<size;i++)
            {
                state node=q.front();
                q.pop();
                if(node.x==target || node.y==target || node.x+node.y==target) return true;
                for(auto &newnode : newstate(node))
                {
                    if(st.find(newnode)==st.end())
                    {
                        st.insert(newnode);
                        q.push(newnode);
                    }
                }
            }
        }
        return false;
    }

private:
    int x;
    int y;
    int target;
    vector<state> newstate(state st)
    {
        vector<state> res;
        int curx=st.x;
        int cury=st.y;
        res.emplace_back(state(0, cury));
        res.emplace_back(state(curx,0));
        res.emplace_back(state(x,cury));
        res.emplace_back(state(curx,y));
        int move=min(curx,y-cury);
        res.emplace_back(state(curx-move,cury+move));
        move=min(cury,x-curx);
        res.emplace_back(state(curx+move,cury-move));
        return res;


    }
    
};
int main()
{
    Solution sol;
    auto res = sol.canMeasureWater(999911, 999913, 1);
    return 0;
}

    