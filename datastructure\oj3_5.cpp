#include <bits/stdc++.h>

using namespace std;

class heap
{
private:
    int n;
    int *elem;
    void shiftdown(int pos)
    {
        if(isLeaf(pos)) return;
        int left_pos=leftChild(pos);
        int right_pos=rightChild(pos);
        int max_pos=pos;
        if(elem[left_pos]>elem[max_pos])
        {
            max_pos=left_pos;
        }
        if(right_pos<n && elem[right_pos]>elem[max_pos])
        {
            max_pos=right_pos;
        }
        if(max_pos!=pos)
        {
            swap(elem[max_pos],elem[pos]);
            shiftdown(max_pos);
        }
    }

public:
    heap() {}
    heap(int m)
    {
        n = m;
        elem = new int[n];
    }
    ~heap()
    {
        delete[] elem;
    }
    void initial()
    {
        for (int i = 0; i < n; i++)
            cin >> elem[i];
    }

    void out()
    {
        for (int i = 0; i < n; i++)
        {
            cout << elem[i];
            if (i <= n - 2)
                cout << "  ";
            else
                cout << endl;
        }
    }

    bool isLeaf(int pos) const
    {
        return ((pos >= n / 2) && pos < n);
    }

    int leftChild(int pos)
    {
        return 2 * pos + 1;
    }

    int rightChild(int pos)
    {
        return 2 * pos + 2;
    }

    void arrange()
    {
        for (int i = (n - 1) / 2; i >= 0; i--)
        {
            shiftdown(i);
        }
    }

    void insertElem(int x)
    {
        int *newelem=new int[n+1];
        for(int i=0;i<n;i++)
        {
            newelem[i]=elem[i];
        }
        newelem[n]=x;
        n++;
        delete[] elem;
        elem=newelem;
        int pos=n-1;
        while(pos>0 && elem[(pos-1)/2]<elem[pos])
        {
            swap(elem[(pos-1)/2],elem[pos]);
            pos=(pos-1)/2;
            
        }
    }
};

int main()
{
    int m;
    cin >> m;
    heap *ex = new heap(m);
    ex->initial();
    ex->arrange();
    ex->out();
    int insData;
    cin >> insData;
    ex->insertElem(insData);
    ex->out();
    return 0;
}
