#include <iostream>
#include <semaphore>

int main() {
    std::counting_semaphore<5> sem(2); // 最大计数5，初始计数2
    std::cout << "Initial count: " << sem.max() << "\n";

    // 持续release而不acquire
    for (int i = 0; i < 10; ++i) {
        sem.release(); // 每次增加计数
        std::cout << "Released " << (i + 1) << " times\n";

        // 尝试获取当前计数(标准库没有直接获取方法)
        // 只能通过尝试acquire来测试
        if (sem.try_acquire()) {
            std::cout << "Can still acquire\n";
            sem.release(); // 立即释放
        } else {
            std::cout << "Cannot acquire - should never happen\n";
        }
    }

    // 最终会怎样？
    // 标准规定：release()超过max()是未定义行为(UB)
}