#include<vector>
using namespace std;
class Solution {
public:
    vector<vector<int>> subsets(vector<int>& nums) {
        int n=nums.size();
        dfs(0,n,nums);
        return res;
    }
    void dfs(int t,int n,vector<int>& nums)
    {
        if(t==n)
        {
            res.push_back(x);
            return;
        }
        dfs(t+1,n,nums);
        x.push_back(nums[t]);
        dfs(t+1,n,nums);
        x.pop_back();
      
    }
private:
    vector<int> x;
    vector<vector<int>> res;
};
int main()
{
    Solution sol;
    vector<int> v{1,2,3};
    auto res=sol.subsets(v);
    return 0;
}