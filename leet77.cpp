#include<vector>
using namespace std;
class Solution {
public:
    vector<vector<int>> combine(int n, int k) {
        vector<int>x;
        num=n;
        dfs(1,k,x);
        return res;

    }
private:
    vector<vector<int>> res;
    int num;
    void dfs(int t,int k,vector<int> x)
    {
       if(k==0)
       {
            res.push_back(x);
            return;
       }
       if(num-t+1<k) return;
        x.push_back(t);
        dfs(t+1,k-1,x);
        x.pop_back();
        dfs(t+1,k,x);

    }

};
int main()
{
    Solution sol;
    auto res=sol.combine(4,2);
    return 0;
}