#include<bits/stdc++.h>
using namespace std;
class Solution {
public:
    int uniquePaths(int m, int n) {
        vector<vector<int>>dp(m+1,vector<int>(n+1,0));
        for(int j=1;j<=n;j++)
        {
            dp[1][j]=1;
        }
        for(int i=2;i<=m;i++)
        {
            for(int j=1;j<=n;j++)
            {
                dp[i][j]=dp[i-1][j]+dp[i][j-1];
            }
        }
        return dp[m][n];

    }
};
int main()
{
    Solution sol;
    int m=3;
    int n=7;
    int res=sol.uniquePaths(m,n);
    cout<<res<<endl;
    return 0;
}