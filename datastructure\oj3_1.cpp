#include <iostream>
using namespace std;

int calculateHeight(int a, int b, int c) {
    if(a+1!=c) return -1;
    if(a==0) return b;
    int height=0;
    int diff=0;
    while(1)
    {
        if(a>1<<height)
        {
            a-=1<<height;
            height++;
        }
        else if(a==1<<height)
        {
            a-=1<<height;
            break;
        }
        else if(a<1<<height)
        {
            diff=(1<<height)-a;
            break;
        }

    }
    b-=diff;
    while(b>0)
    {
        b-=c;
        height++;
    }
    return height+1;
    
}

int main() {
    int T, a, b, c;
    cin>>T;
    while(T--)
    {
    cin>>a>>b>>c;
    cout<<calculateHeight(a, b, c)<<endl;
    }
    return 0;
}