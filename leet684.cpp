#include<vector>
#include<unordered_map>
using namespace std;
class Solution
{
public:
    vector<int> findRedundantConnection(vector<vector<int>> &edges)
    {
        n=edges.size();
        fa.emplace_back(0);
        for(int i=0;i<n;i++)
        {
            fa.emplace_back(i+1);
        }
        for(int i=0;i<n;i++)
        {
            int x=edges[i][0];
            int y=edges[i][1];
            if(find(x)==find(y))
            {
                return edges[i];
            }
            else
            {
                merge(x,y);
            }
            
        }
        return edges[n-1];
    }
private:
    vector<int> fa;
    int n;
    inline int find(int x)
    {
        return x==fa[x]? x:(fa[x]=find(fa[x]));
    }
    void merge(int i,int j)
    {
        fa[find(i)]=find(j);
    }
    
};