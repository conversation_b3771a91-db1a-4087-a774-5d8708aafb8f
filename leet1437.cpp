#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    bool kLengthApart(vector<int> &nums, int k)
    {
        int cnt = 0;
        auto it = find(nums.begin(), nums.end(), 1);
        if (it == nums.end())
        {
            return true;
        }
        int i = it - nums.begin() + 1;
        for (; i < nums.size(); i++)
        {
            if (nums[i] == 1)
            {
                if (cnt < k)
                {
                    return false;
                }
                cnt = 0;
            }
            else if (nums[i] == 0)
            {
                cnt++;
            }
        }
        return true;
    }
};
int main()
{
    Solution s;
    vector<int> nums = {1, 0, 0, 0, 1, 0, 0, 1};
    cout << s.kLengthApart(nums, 2);
    return 0;
}