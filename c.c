#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <limits.h>

#define SIZE 5 // 定义棋盘的大小

// 节点结构体
typedef struct Node
{
    char state[SIZE + 1];
    int g; // 从起点到当前节点的代价
    int h; // 启发式函数值
    struct Node *parent;
} Node;

// 计算启发式函数 h(n)
int heuristic(const char *state)
{
    int h = 0;
    for (int i = 0; i < SIZE; i++)
    {
        if (state[i] == 'W')
        {
            for (int j = 0; j < i; j++)
            {
                if (state[j] == 'B')
                {
                    h++;
                }
            }
        }
    }
    return h;
}

// 生成新节点
Node *createNode(const char *state, int g, int h, Node *parent)
{
    Node *newNode = (Node *)malloc(sizeof(Node));
    strcpy(newNode->state, state);
    newNode->g = g;
    newNode->h = h;
    newNode->parent = parent;
    return newNode;
}

// 比较函数用于排序 openList
int cmp(const void *a, const void *b)
{
    Node *nodeA = *(Node **)a;
    Node *nodeB = *(Node **)b;
    int fA = nodeA->g + 3 * nodeA->h;
    int fB = nodeB->g + 3 * nodeB->h;
    return fA - fB;
}

// 检查目标状态是否达到
int isGoal(const char *state)
{
    int seenB = 0;
    for (int i = 0; i < SIZE; i++)
    {
        if (state[i] == 'B')
            seenB = 1;
        if (state[i] == 'W' && seenB)
            return 0;
    }
    return 1;
}

// 打印路径
void printPath(Node *node)
{
    if (node == NULL)
        return;
    printPath(node->parent);
    printf("%s\n", node->state);
}

// A* 算法
void A_star(const char *initialState)
{
    Node *openList[1000];
    int openCount = 0;
    int visited[1000] = {0};

    Node *startNode = createNode(initialState, 0, heuristic(initialState), NULL);
    openList[openCount++] = startNode;

    while (openCount > 0)
    {
        qsort(openList, openCount, sizeof(Node *), cmp);
        Node *currentNode = openList[0];
        memmove(&openList[0], &openList[1], (--openCount) * sizeof(Node *));

        if (isGoal(currentNode->state))
        {
            printf("Final State: %s\n", currentNode->state);
            printf("Moves:\n");
            printPath(currentNode);
            printf("Total Cost: %d\n", currentNode->g);
            return;
        }

        char state[SIZE + 1];
        strcpy(state, currentNode->state);
        int emptyIndex = strchr(state, 'E') - state;

        // 遍历可能的移动
        for (int i = 0; i < SIZE; i++)
        {
            if (i != emptyIndex && abs(i - emptyIndex) <= 2)
            {
                strcpy(state, currentNode->state);
                char temp = state[i];
                state[i] = state[emptyIndex];
                state[emptyIndex] = temp;

                int moveCost = abs(i - emptyIndex) > 1 ? abs(i - emptyIndex) : 1;
                Node *newNode = createNode(state, currentNode->g + moveCost, heuristic(state), currentNode);

                openList[openCount++] = newNode;
            }
        }
    }
    printf("No solution found.\n");
}

int main()
{
    char initialState[SIZE + 1] = "BBWWE";
    A_star(initialState);
    return 0;
}
