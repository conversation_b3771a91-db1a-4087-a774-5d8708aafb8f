#include<bits/stdc++.h>
using namespace std;
class Solution {
public:
    string intToRoman(int num) {
        int val[]={1000,900,500,400,100,90,50,40,10,9,5,4,1};
        string rep[]={"M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"};
        string res="";
        for(int i=0;i<13;i++)
        {
            while(num>=val[i])
            {
                num-=val[i];
                res+=rep[i];
            }
            if(num==0) break;
        }
        return res;
    }
};
int main()
{
    Solution sol;
    string s=sol.intToRoman(9);
    cout<<s<<endl;
    return 0;
}