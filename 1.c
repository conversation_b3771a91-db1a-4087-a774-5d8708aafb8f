#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define MAX_NODES 100
#define INFINITY 9999

typedef struct
{
    char state[6];
    int g; // 实际代价
    int h; // 启发式估计代价
    int f; // f = g + h
    struct Node *parent;
} Node;

Node openList[MAX_NODES];
int openListSize = 0;
Node closedList[MAX_NODES];
int closedListSize = 0;

// 启发式函数，计算每个W左边的B的个数
int heuristic(char state[])
{
    int h = 0;
    for (int i = 0; i < 5; i++)
    {
        if (state[i] == 'W')
        {
            for (int j = i + 1; j < 5; j++)
            {
                if (state[j] == 'B')
                {
                    h++;
                }
            }
        }
    }
    return h;
}

// 比较函数，用于排序openList
int cmp(const void *a, const void *b)
{
    Node *nodeA = (Node *)a;
    Node *nodeB = (Node *)b;
    return nodeA->f - nodeB->f;
}

// 检查状态是否在列表中
int isInList(Node list[], int size, char state[])
{
    for (int i = 0; i < size; i++)
    {
        if (strcmp(list[i].state, state) == 0)
        {
            return 1;
        }
    }
    return 0;
}

// 添加节点到openList
void addToOpen(Node node)
{
    openList[openListSize++] = node;
    qsort(openList, openListSize, sizeof(Node), cmp);
}

// A*算法主函数
void A_star(char initialState[], char goalState[])
{
    Node startNode = {initialState, 0, heuristic(initialState), heuristic(initialState), NULL};
    addToOpen(startNode);

    while (openListSize > 0)
    {
        Node *current = &openList[0];
        openListSize--;
        for (int i = 0; i < openListSize; i++)
        {
            openList[i] = openList[i + 1];
        }

        if (strcmp(current->state, goalState) == 0)
        {
            // 找到目标状态，打印结果
            printf("Total cost: %d\n", current->g);
            printf("Final state: %s\n", current->state);
            while (current != NULL)
            {
                printf("%s\n", current->state);
                current = current->parent;
            }
            return;
        }

        closedList[closedListSize++] = *current;

        // 生成子节点
        for (int i = 0; i < 5; i++)
        {
            if (current->state[i] == 'E')
            {
                // 移动相邻的将牌
                if (i > 0 && current->state[i - 1] != 'E')
                {
                    char newState[6];
                    strcpy(newState, current->state);
                    newState[i] = newState[i - 1];
                    newState[i - 1] = 'E';
                    if (!isInList(closedList, closedListSize, newState))
                    {
                        Node newNode = {newState, current->g + 1, heuristic(newState), heuristic(newState) + current->g + 1, current};
                        addToOpen(newNode);
                    }
                }
                if (i < 4 && current->state[i + 1] != 'E')
                {
                    char newState[6];
                    strcpy(newState, current->state);
                    newState[i] = newState[i + 1];
                    newState[i + 1] = 'E';
                    if (!isInList(closedList, closedListSize, newState))
                    {
                        Node newNode = {newState, current->g + 1, heuristic(newState), heuristic(newState) + current->g + 1, current};
                        addToOpen(newNode);
                    }
                }
                // 跳跃将牌
                if (i > 1 && current->state[i - 2] != 'E')
                {
                    char newState[6];
                    strcpy(newState, current->state);
                    newState[i] = newState[i - 2];
                    newState[i - 2] = 'E';
                    if (!isInList(closedList, closedListSize, newState))
                    {
                        Node newNode = {newState, current->g + 2, heuristic(newState), heuristic(newState) + current->g + 2, current};
                        addToOpen(newNode);
                    }
                }
                if (i < 3 && current->state[i + 2] != 'E')
                {
                    char newState[6];
                    strcpy(newState, current->state);
                    newState[i] = newState[i + 2];
                    newState[i + 2] = 'E';
                    if (!isInList(closedList, closedListSize, newState))
                    {
                        Node newNode = {newState, current->g + 2, heuristic(newState), heuristic(newState) + current->g + 2, current};
                        addToOpen(newNode);
                    }
                }
            }
        }
    }

    printf("No solution found.\n");
}

int main()
{
    char initialState[] = "BBWWE";
    char goalState[] = "WWWBB";
    A_star(initialState, goalState);
    return 0;
}
