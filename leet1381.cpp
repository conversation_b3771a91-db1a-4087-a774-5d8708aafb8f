#include <vector>
using namespace std;
class CustomStack {
  public:
    vector<int> v;
    int size;
    int maxsize;
    CustomStack(int maxSize) {
        size = 0;
        maxsize = maxSize;
    }

    void push(int x) {
        if (size < maxsize) {
            v.push_back(x);
            size++;
        }
    }

    int pop() {
        if (size == 0)
            return -1;
        int ans = v[size - 1];
        v.pop_back();
        size--;
        return ans;
    }

    void increment(int k, int val) {
        int i = 0;
        while (i < k && i < size) {
            v[i] += val;
            i++;
        }
    }
};

/**
 * Your CustomStack object will be instantiated and called as such:
 * CustomStack* obj = new CustomStack(maxSize);
 * obj->push(x);
 * int param_2 = obj->pop();
 * obj->increment(k,val);
 */
int main() {
    CustomStack stk = CustomStack(3); // 栈是空的 []
    stk.push(1);                      // 栈变为 [1]
    stk.push(2);                      // 栈变为 [1, 2]
    stk.pop();   // 返回 2 --> 返回栈顶值 2，栈变为 [1]
    stk.push(2); // 栈变为 [1, 2]
    stk.push(3); // 栈变为 [1, 2, 3]
    stk.push(4); // 栈仍然是 [1, 2, 3]，不能添加其他元素使栈大小变为 4
    stk.increment(5, 100); // 栈变为 [101, 102, 103]
    stk.increment(2, 100); // 栈变为 [201, 202, 103]
    stk.pop(); // 返回 103 --> 返回栈顶值 103，栈变为 [201, 202]
    stk.pop(); // 返回 202 --> 返回栈顶值 202，栈变为 [201]
    stk.pop(); // 返回 201 --> 返回栈顶值 201，栈变为 []
    stk.pop(); // 返回 -1 --> 栈为空，返回 -1
}