#include<bits/stdc++.h>
using namespace std;
bool prime(int n)  //判断素数
{
    if(n==1) return false;
    if(n==2 or n==3) return true;
    for(int i=2;i*i<=n;i++)
    {
        if(n%i==0) return false;
    }
    return true;
}
pair<int,int> num_divisor(int n) //求出约数个数
{
    vector<int> v;
    int temp_n=n;
    for(int i=2;i*i<=n;i++)
    {
        if(prime(i))
        {
            int count=0;
            while(temp_n%i==0)
            {
                count++;
                temp_n/=i;
            }    //如果能被素数整除,求出整除个数count,将count+1加入到vector中
            v.push_back(count+1);
        }
    }
    int result=1;
    for(int i=0;i<v.size();i++)
    {
        result*=v[i];   //求出约数个数
    }
    return make_pair(result,n);
}
int main()
{
    int max=0,num;
    int a,b;
    cin>>a>>b;
    for(int i=a;i<=b;i++)
    {
        if(num_divisor(i).first>max)
        {
            max=num_divisor(i).first;
            num=num_divisor(i).second;
        }
    }
    cout<<max<<" "<<num<<endl;
}