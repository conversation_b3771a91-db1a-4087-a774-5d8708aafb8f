#include <iostream>
using namespace std;
long long m,n,a,b;
long long MyPow(long long x,long long y,long long m)
{
    if(y==0)   //A^0==1
    {
        return 1;
    }
    else
    {
        long long ans=MyPow(x,y>>1,m);
        //ans=a^(b/2),使用移位操作代替除以2,能够提升程序性能
        if(!(y&1))
        //二进制表示下的偶数最后一位必定为0,所以可以用按位与计算是否为偶数
        {
            return (ans*ans)%m;
        }
        else
        {
            return (ans*ans*x)%m;
        }
    }
}
int main()
{
    long long res=0;
    scanf("%I64d %I64d",&m,&n);
    while(n--)
    {
        scanf("%I64d %I64d",&a,&b);
        res+=(MyPow(a%m,b,m));
        res%=m;
    }
    printf("%I64d\n",res);
    return 0;
}
