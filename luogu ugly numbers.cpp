#include<queue>
#include<iostream>
#include<unordered_set>
using namespace std;
int main()
{
    priority_queue<long long,vector<long long>, greater<long long> > q;
    unordered_set<long long> st;
    q.push(1);
    long long mul[]={2,3,5};
    vector<long long> ans;
    for(long long i=0;i<1500;i++)
    {
        long long temp=q.top();
        ans.push_back(temp);
        q.pop();
        for(long long i=0;i<3;i++)
        {
            long long t=temp*mul[i];
            if(st.find(t)==st.end())
            {
                st.emplace(t);
                q.push(t);
            }
        }
        
    }
    cout<<"The 1500'th ugly number is "<<ans[1499]<<endl;

    
}