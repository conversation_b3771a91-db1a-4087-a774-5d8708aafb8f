#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    int minimumFinishTime(vector<vector<int>> &tires, int changeTime, int numLaps)
    {
        int n = tires.size();
        vector<long long> dp(numLaps + 1, LLONG_MAX);
        dp[0] = 0;
        for (int i = 1; i <= numLaps; i++)
        {
            long long minTime = LLONG_MAX;
            for (int j = 0; j < n; j++)
            {
                long long currTime = tires[j][0];
                int currLaps = 1;
                while (currLaps < i)
                {
                    currTime += tires[j][1];
                    currLaps++;
                }
                minTime = min(minTime, currTime);
            }
            if (i > 1)
            {
                minTime += changeTime;
            }
            dp[i] = min(dp[i], minTime);
            for (int k = 1; k < i; k++)
            {

                dp[i] = min(dp[i], dp[k] + dp[i - k] + changeTime);
            }
        }
        return dp[numLaps];
    }
};