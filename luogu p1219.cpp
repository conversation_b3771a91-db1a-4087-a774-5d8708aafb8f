#include<iostream>
#include<vector>
using namespace std;
vector<vector<int>> res;
vector<int> x;
bool judge(int k)
{
    for(int i=0;i<k;i++)
    {
        if(abs(i-k)==abs(x[i]-x[k])) return false;
    }
    return true;
}
void dfs(int t,int n)
{
    if(t==n)
    {
        res.push_back(x);
        return;
    }
    for(int i=t;i<n;i++)
    {
        swap(x[i],x[t]);
        if(judge(t))
        {
            dfs(t+1,n);
        }
        swap(x[i],x[t]);
    }
}
int main()
{
    int n;
    cin>>n;
    for(int i=0;i<n;i++)
    {
        x.push_back(i+1);
    }
    dfs(0,n);
    for(int i=0;i<3;i++)
    {
        for(int j=0;j<n;j++)
        {
            cout<<res[i][j]<<" ";
        }
        printf("\n");

    }
    cout<<res.size()<<endl;
    return 0;

}