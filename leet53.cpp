#include<bits/stdc++.h>
using namespace std;
class Solution {
public:
    int maxSubArray(vector<int>& nums) {
        vector<int> dp(nums.size());
        dp[0]=nums[0];
        int size=nums.size();
        int res=dp[0];
        for(int i=1;i<size;i++)
        {
            dp[i]=max(nums[i],dp[i-1]+nums[i]);
            res=max(res,dp[i]);
            
        }
        return res;
        



    }
};
int main()
{
    Solution sol;
    vector<int> v{-2,1,-3,4,-1,2,1,-5,4};
    int res=sol.maxSubArray(v);
    cout<<res<<endl;
    return 0;
}