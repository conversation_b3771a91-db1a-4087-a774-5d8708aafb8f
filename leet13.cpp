#include<bits/stdc++.h>
using namespace std;
class Solution {
public:
    int romanToInt(string s) {
        int sum=0;
        int prenum=getValue(s[0]);
        int s_len=s.length();
        int num;
        for(int i=1;i<s_len;i++)
        {
            num=getValue(s[i]);
            if(prenum<num)
            {
                sum-=prenum;
            }
            else
            {
                sum+=prenum;
            }
            prenum=num;
        }
        sum+=prenum;
        return sum;

    }
private:
    int getValue(char c)
    {
        switch (c)
        {
        case 'I': return 1;
            break;
        case 'V': return 5;
        case 'X': return 10;
        case 'L': return 50;
        case 'C': return 100;
        case 'D': return 500;
        case 'M': return 1000;
        default: return 0;
            break;
        }
    }
};
int main()
{
    Solution sol;
    int x=sol.romanToInt("IX");
    cout<<x<<endl;
    return 0;
    return 0;
}