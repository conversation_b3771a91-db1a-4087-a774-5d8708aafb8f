#include<queue>
using namespace std;

  struct TreeNode {
      int val;
      TreeNode *left;
      TreeNode *right;
      TreeNode(int x) : val(x), left(NULL), right(NULL) {}
  };
 

class Solution {
public:
    TreeNode* getTargetCopy(TreeNode* original, TreeNode* cloned, TreeNode* target) {
        if(original==target) return cloned;
        if(original->left!=NULL)
        {
            TreeNode* temp=getTargetCopy(original->left,cloned->left,target);
            if(temp!=NULL) return temp;
        }
        if(original->right!=NULL)
        {
            TreeNode* temp=getTargetCopy(original->right,cloned->right,target);
            if(temp!=NULL) return temp;
        }
        return NULL;

    }
};