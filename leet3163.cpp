#include <bits/stdc++.h>
using namespace std;
class Solution {
  public:
    string compressedString(string word) {
        string comp;
        int i = 0;
        int n = word.length();

        while (i < n) {
            char currentChar = word[i];
            int count = 0;

            // Count consecutive same characters (up to 9)
            while (i < n && count < 9 && word[i] == currentChar) {
                count++;
                i++;
            }

            // Append count and character to result
            comp += to_string(count) + currentChar;
        }

        return comp;
    }
};