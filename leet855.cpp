#include <bits/stdc++.h>
using namespace std;
class ExamRoom
{
private:
    int n;
    set<int> s;

public:
    ExamRoom(int n)
    {
        this->n = n;
    }

    int seat()
    {
        if (s.empty())
        {
            s.insert(0);
            return 0;
        }

        int prev = *s.begin();
        int maxdist = 0; // 修改这里：初始值应为0
        int ans = 0;

        // 考虑开头位置
        if (*s.begin() > maxdist)
        {
            maxdist = *s.begin();
            ans = 0;
        }

        // 考虑中间位置
        for (auto it = next(s.begin()); it != s.end(); it++)
        {
            int cur = *it;
            int dist = (cur - prev) / 2;
            if (dist > maxdist)
            {
                maxdist = dist;
                ans = prev + dist;
            }
            prev = cur;
        }

        // 考虑结尾位置
        if (n - 1 - *s.rbegin() > maxdist)
        {
            ans = n - 1;
        }
        s.insert(ans);
        return ans;
    }

    void leave(int p)
    {
        s.erase(p);
    }
};

/**
 * Your ExamRoom object will be instantiated and called as such:
 * ExamRoom* obj = new ExamRoom(n);
 * int param_1 = obj->seat();
 * obj->leave(p);
 */