#include <bits/stdc++.h>
using namespace std;
class node
{
public:
    int p;
    int v;
};
int main()
{
    int n, k;
    cin >> n >> k;
    k--;
    vector<node> v(n);
    for (int i = 0; i < n; i++)
    {
        cin >> v[i].p;
    }
    for (int i = 0; i < n; i++)
    {
        cin >> v[i].v;
    }
    int sp = v[k].p;
    int sv = v[k].v;
    sort(v.begin(), v.end(), [](node a, node b)
         { return a.p < b.p; });
    int vmax = sv, vmin = sv;
    for (int i = 0; i < n; i++)
    {
        if (v[i].p > sp)
        {
            break;
        }
        if (v[i].v > vmax)
        {
            vmax = v[i].v;
        }
    }
    for (int i = 0; i < n; i++)
    {
        if (v[i].p < sp)
        {
            continue;
        }
        if (v[i].v < vmin)
        {
            vmin = v[i].v;
        }
    }
    int cnt = 0;
    for (int i = 0; i < n; i++)
    {
        if (v[i].p == sp)
        {
            cnt++;
        }
        if (v[i].p > sp && v[i].v < vmax)
        {
            cnt++;
        }
        if (v[i].p < sp && v[i].v > vmin)
        {
            cnt++;
        }
    }
    cout << cnt << endl;
    return 0;
}