#include<bits/stdc++.h>
using namespace std;
int a[10001];
void f(int *a,int l,int r,int &max,int &min)
{

    if(l==r)
    {
        max=a[l],min=a[l];
    }
    else
    {
        int lmax=a[l],lmin=a[l],rmax=a[r],rmin=a[r];
        int middle=(l+r)/2;
        f(a,l,middle,lmax,lmin);
        f(a,middle+1,r,rmax,rmin);
        max=lmax>rmax? lmax:rmax;
        min=lmin<rmin? lmin:rmin;
        
    }
}
int main()
{
    fstream file;
    file.open("lab1 data.txt",ios::in);
    int i=0;
    while(file>>a[i++]);
    file.close();
    int max=a[0],min=a[0];
    int len=sizeof(a)/sizeof(a[0]);
    f(a,0,len-1,max,min);
    cout<<max<<" "<<min<<endl;
    return 0;
}