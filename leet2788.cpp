#include <vector>
#include <string>
using namespace std;
class Solution {
public:
    vector<string> splitWordsBySeparator(vector<string>& words, char separator) {
        vector<string> ans;
        for(int i=0;i<words.size();i++)
        {
            string s=words[i];
            string temp="";
            int index=0;
            for(int j=0;j<s.size();j++)
            {
                if(s[j]==separator )
                {
                    temp=s.substr(index,j-index);
                    if(temp.size()>0)
                    {
                        ans.push_back(temp);
                    }
                    index=j+1;
                }
                else if(j==s.size()-1)
                {
                    temp=s.substr(index,j-index+1);
                    ans.push_back(temp);
                }
            }
        }
        return ans;
        
    }
};
int main()
{
    Solution s;
    vector<string> words{"$easy$","$problem$"};
    auto ans=s.splitWordsBySeparator(words,'$');
    return 0;
}