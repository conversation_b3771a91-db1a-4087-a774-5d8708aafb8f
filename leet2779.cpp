#include <bits/stdc++.h>
using namespace std;
class Solution
{
public:
    int maximumBeauty(vector<int> &nums, int k)
    {
        sort(nums.begin(), nums.end());
        int ans = 0, left = 0;
        int n = nums.size();
        for (int i = 0; i < n; i++)
        {
            while (nums[i] - nums[left] > 2 * k)
            {
                left++;
            }
            ans = max(ans, i - left + 1);
        }
        return ans;
    }
};