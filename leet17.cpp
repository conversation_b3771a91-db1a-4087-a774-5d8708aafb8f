#include <bits/stdc++.h>
using namespace std;
class Solution {
  public:
    vector<string> res;
    string d[10] = {"", "", "abc", "def", "ghi", "jkl", "mno", "pqrs", "tuv", "wxyz"};
    vector<string> letterCombinations(string digits) {
        if (digits.empty())
            return res;
        dfs(digits, "", 0);
        return res;
    }
    void dfs(string digits, string tmp, int index) {
        if (index == digits.length()) {
            res.push_back(tmp);
            return;
        }
        char c = digits[index];
        string letters = d[c - '0'];
        for (int i = 0; i < letters.length(); i++) {
            dfs(digits, tmp + letters[i], index + 1);
        }
    }
};