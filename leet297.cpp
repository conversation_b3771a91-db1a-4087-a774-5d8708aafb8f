#include <bits/stdc++.h>
using namespace std;
struct TreeNode
{
    int val;
    TreeNode *left;
    TreeNode *right;
    TreeNode(int x) : val(x), left(NULL), right(NULL) {}
};
// class Codec
// {
// public:
//     // Encodes a tree to a single string.
//     string serialize(TreeNode *root)
//     {
//         if (!root)
//             return "[]";
//         string res = "";
//         queue<TreeNode *> q;
//         q.push(root);
//         while (!q.empty())
//         {
//             TreeNode *node = q.front();
//             q.pop();
//             if (node)
//             {
//                 res += to_string(node->val);
//                 res += ",";
//                 q.push(node->left);
//                 q.push(node->right);
//             }
//             else
//                 res += "null,";
//         }
//         res.pop_back();
//         return res;
//     }

//     // Decodes your encoded data to tree.
//     TreeNode *deserialize(string data)
//     {
//         if (data.size() == 2)
//             return NULL;
//         vector<string> tokens;
//         split(data, tokens, ",");
//         queue<TreeNode *> q;
//         TreeNode *root = new TreeNode(stoi(tokens[0]));
//         q.push(root);
//         int i=1;
//         while(!q.empty())
//         {
//             TreeNode *node = q.front();
//             q.pop();
//             if(tokens[i] != "null")
//             {
//                 node->left = new TreeNode(stoi(tokens[i]));
//                 q.push(node->left);
//             }
//             i++;
//             if(tokens[i] != "null")
//             {
//                 node->right = new TreeNode(stoi(tokens[i]));
//                 q.push(node->right);

//             }
//             i++;
//         }
//         return root;

//     }

// private:
//     void split(const string &s, vector<string> &tokens, const string &delimiters = " ")
//     {
//         string::size_type lastPos = s.find_first_not_of(delimiters, 0);
//         string::size_type pos = s.find_first_of(delimiters, lastPos);
//         while (string::npos != pos || string::npos != lastPos)
//         {
//             tokens.push_back(s.substr(lastPos, pos - lastPos)); // use emplace_back after C++11
//             lastPos = s.find_first_not_of(delimiters, pos);
//             pos = s.find_first_of(delimiters, lastPos);
//         }
//     }
// };

// // Your Codec object will be instantiated and called as such:
// // Codec ser, deser;
// // TreeNode* ans = deser.deserialize(ser.serialize(root));
/**
 * Definition for a binary tree node.
 * struct TreeNode {
 *     int val;
 *     TreeNode *left;
 *     TreeNode *right;
 *     TreeNode(int x) : val(x), left(NULL), right(NULL) {}
 * };
 */
// DFS
// root -> left subtree -> right subtree pre-order

class Codec
{
public:
    string serialize(TreeNode *root)
    {
        ostringstream out;
        serialize(root, out);
        return out.str();
    }

    TreeNode *deserialize(string data)
    {
        istringstream in(data);
        return deserialize(in);
    }

private:
    void serialize(TreeNode *root, ostringstream &out)
    {
        if (root == NULL)
            out << "# ";
        else
        {
            out << root->val << ' ';
            serialize(root->left, out);
            serialize(root->right, out);
        }
    }

    TreeNode *deserialize(istringstream &in)
    {
        string val;
        in >> val;
        if (val == "#")
            return NULL;
        else
        {
            TreeNode *root = new TreeNode(stoi(val));
            root->left = deserialize(in);
            root->right = deserialize(in);
            return root;
        }
    }
};

// Your Codec object will be instantiated and called as such:
// Codec codec;
// codec.deserialize(codec.serialize(root));
int main()
{
    TreeNode *root = new TreeNode(1);
    root->left = new TreeNode(2);
    root->right = new TreeNode(3);
    Codec codec;
    string s = codec.serialize(root);
    cout << s;
    TreeNode *root2 = codec.deserialize(s);
    cout << root2->val;
    cout << root2->left->val;
    cout << root2->right->val;
    return 0;
}