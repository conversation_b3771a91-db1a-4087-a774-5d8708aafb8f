#include <iostream>
#include <mutex> // Include the <mutex> header for std::unique_lock
#include <shared_mutex>
#include <thread>
#include <vector>

std::shared_mutex rw_mutex;
int shared_data = 0;

void reader(int id) {
    std::shared_lock<std::shared_mutex> lock(rw_mutex);
    std::cout << "Reader " << id << " sees value: " << shared_data << "\n";
}

void writer(int id, int value) {
    std::unique_lock<std::shared_mutex> lock(rw_mutex);
    shared_data = value;
    std::cout << "Writer " << id << " updated value to: " << value << "\n";
}

int main() {
    std::vector<std::thread> threads;

    // 创建多个读者和写者
    for (int i = 0; i < 5; ++i) {
        threads.emplace_back(reader, i);
    }

    for (int i = 0; i < 2; ++i) {
        threads.emplace_back(writer, i, i * 10);
    }

    for (int i = 5; i < 10; ++i) {
        threads.emplace_back(reader, i);
    }

    for (auto &t : threads)
        t.join();
    return 0;
}