#include<queue>
#include<unordered_map>
using namespace std;
class Solution {
public:
    bool canFinish(int numCourses, vector<vector<int>>& prerequisites) {
        vector<int> indegree(numCourses, 0);
        unordered_map<int, vector<int>> adj;
        int cnt=0;
        for(int i=0;i<prerequisites.size();i++)
        {
            indegree[prerequisites[i][0]]++;
            adj[prerequisites[i][1]].push_back(prerequisites[i][0]);
        }
        queue<int> q;
        for(int i=0;i<numCourses;i++)
        {
            if(indegree[i]==0)
            {
                q.push(i);
            }
        }
        while(!q.empty())
        {
            int u=q.front();
            q.pop();
            cnt++;
            for(int i=0;i<adj[u].size();i++)
            {
                if(--indegree[adj[u][i]]==0)
                {
                    q.push(adj[u][i]);
                }
            }
        }
        return cnt==numCourses;

    }
};