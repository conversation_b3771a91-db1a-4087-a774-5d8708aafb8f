#include <bits/stdc++.h>
using namespace std;
int main()
{
    ios::sync_with_stdio(false);
    cin.tie(0);
    cout.tie(0);
    int k;
    cin >> k;
    string s;
    cin >> s;
    int x = atoi(s.c_str());
    unordered_map<int, int> m;
    int c = 0;
    for (int i = 0; i < s.size(); i++)
    {
        c += s[i] - '0';
        m[s[i] - '0']++;
    }
    int cnt = 0;
    for (int i = 0; i < 9; i++)
    {
        if (m[i] == 0)
        {
            continue;
        }
        while (c < k && m[i])
        {
            c += 9 - i;
            cnt++;
            m[i]--;
        }
        if (c >= k)
        {
            break;
        }
    }
    cout << cnt << endl;
    return 0;
}