#include<cstdio>
#include<math.h>
#include<algorithm>
#include<iostream>
using namespace std;
double x,y,c;
double check(double d)
{
	return 1-c/sqrt(y*y-d*d)-c/sqrt(x*x-d*d);
}
int main()
{
    double mid;
	while(cin>>x)
	{
        cin>>y>>c;
		double r=min(x,y);
		double l=0;
		while(r-l>0.00000001)
		{	
			mid=(l+r)/2;
			if(check(mid)>0)
			l=mid;
			if(check(mid)<0)
			r=mid;
		}
		printf("%.3lf\n",mid);
	}
		return 0;	
}