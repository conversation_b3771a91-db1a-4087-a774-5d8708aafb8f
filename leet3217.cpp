#include <bits/stdc++.h>
#include <memory>
using namespace std;
struct ListNode
{
    int val;
    ListNode *next;
    ListNode() : val(0), next(nullptr) {}
    ListNode(int x) : val(x), next(nullptr) {}
    ListNode(int x, ListNode *next) : val(x), next(next) {}
};

class Solution
{
public:
    ListNode *modifiedList(vector<int> &nums, ListNode *head)
    {
        auto ptr = make_unique<int>(5);
        unordered_set<int> mp(nums.begin(), nums.end());
        while (mp.count(head->val))
        {
            head = head->next;
        }
        if (!head->next)
            return head;
        ListNode *curr = head->next;
        ListNode *prev = head;
        while (true)
        {
            if (mp.count(curr->val))
            {
                prev->next = curr->next;
                curr = curr->next;
                if (!curr)
                    break;
            }
            else
            {
                prev = curr;
                curr = curr->next;
                if (!curr)
                    break;
            }
        }
        return head;
    }
};