#include <chrono>
#include <condition_variable>
#include <iostream>
#include <mutex>
#include <random> // For random sleep times
#include <string> // For philosopher names/IDs
#include <thread>
#include <vector>

// --- Configuration ---
const int NUM_PHILOSOPHERS = 5;
const int EAT_COUNT_LIMIT = 3; // Limit how many times each philosopher eats for demo purposes

// --- State Definitions ---
enum class State {
    THINKING,
    HUNGRY,
    EATING
};

// --- Shared Resources ---
std::vector<State> philosopher_states(NUM_PHILOSOPHERS);
std::mutex mtx; // Mutex to protect access to philosopher_states
// One condition variable per philosopher, to wait if they cannot eat
std::vector<std::condition_variable> cv(NUM_PHILOSOPHERS);
// Track how many times each philosopher ate
std::vector<int> eat_counts(NUM_PHILOSOPHERS, 0);

// --- Helper Functions ---
int left(int i) {
    return (i + NUM_PHILOSOPHERS - 1) % NUM_PHILOSOPHERS;
}

int right(int i) {
    return (i + 1) % NUM_PHILOSOPHERS;
}

// Utility for random sleep
std::default_random_engine generator(std::chrono::system_clock::now().time_since_epoch().count());
std::uniform_int_distribution<int> distribution(100, 500); // milliseconds

void print_state(int id, const std::string &action) {
    // It's generally safer to lock cout as well if multiple threads might print
    // concurrently outside the main lock, but here we print inside the lock mostly.
    // For simplicity, we'll assume occasional interleaved output is acceptable for demo.
    std::cout << "Philosopher " << id << " " << action << std::endl;
}

// --- Core Logic ---

// Checks if philosopher 'id' can eat, and if so, sets state and notifies.
// MUST be called with the mutex 'mtx' locked.
void test(int id) {
    if (philosopher_states[id] == State::HUNGRY && philosopher_states[left(id)] != State::EATING &&
        philosopher_states[right(id)] != State::EATING) {
        philosopher_states[id] = State::EATING;
        // No need to print here, pickup/putdown will handle printing state changes
        // print_state(id, "starts eating"); // Optional: print inside test
        cv[id].notify_one(); // Wake up the philosopher waiting in pickup()
    }
}

// Philosopher 'id' tries to pick up chopsticks
void pickup(int id) {
    std::unique_lock<std::mutex> lock(mtx); // Lock the mutex
    print_state(id, "becomes hungry");
    philosopher_states[id] = State::HUNGRY;

    // Try to start eating immediately
    test(id);

    // If test() didn't set state to EATING, wait
    while (philosopher_states[id] != State::EATING) {
        // print_state(id, "is waiting for chopsticks..."); // Optional debug
        // wait() atomically releases the lock and waits.
        // When woken up, it re-acquires the lock before returning.
        cv[id].wait(lock);
    }
    // Now state must be EATING
    print_state(id, "picks up chopsticks and starts eating");
    // Lock is automatically released when 'lock' goes out of scope if we exit here,
    // but we are still inside the philosopher's eat cycle.
}

// Philosopher 'id' puts down chopsticks
void putdown(int id) {
    std::unique_lock<std::mutex> lock(mtx); // Lock the mutex
    print_state(id, "finishes eating and puts down chopsticks");
    philosopher_states[id] = State::THINKING;
    eat_counts[id]++; // Increment eat count

    // See if neighbors can now eat
    test(left(id));
    test(right(id));

    // Lock is automatically released when 'lock' goes out of scope.
}

// The routine for each philosopher thread
void philosopher_thread(int id) {
    while (eat_counts[id] < EAT_COUNT_LIMIT) {
        // 1. Think
        { // Scoping the lock just for printing state
            std::lock_guard<std::mutex> lock(mtx);
            print_state(id, "is thinking");
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(distribution(generator)));

        // 2. Become hungry and try to pick up chopsticks
        pickup(id); // Handles acquiring chopsticks and waiting if necessary

        // 3. Eat
        // Eating simulation is implicitly done between pickup() finishing and putdown() starting.
        // print_state(id, "is eating"); // Already printed in pickup
        std::this_thread::sleep_for(std::chrono::milliseconds(distribution(generator)));

        // 4. Put down chopsticks
        putdown(id); // Handles releasing chopsticks and notifying neighbors
    }
    // Print final state after finishing eating quota
    {
        std::lock_guard<std::mutex> lock(mtx);
        print_state(id, "finished their meals.");
    }
}

// --- Main Function ---
int main() {
    std::cout << "Dining Philosophers Problem using C++ Condition Variables" << std::endl;
    std::cout << "---------------------------------------------------------" << std::endl;

    // Initialize states to THINKING
    for (int i = 0; i < NUM_PHILOSOPHERS; ++i) {
        philosopher_states[i] = State::THINKING;
    }

    // Create philosopher threads
    std::vector<std::thread> threads;
    for (int i = 0; i < NUM_PHILOSOPHERS; ++i) {
        threads.emplace_back(philosopher_thread, i);
    }

    // Wait for all threads to complete
    for (std::thread &t : threads) {
        t.join();
    }

    std::cout << "---------------------------------------------------------" << std::endl;
    std::cout << "All philosophers have finished their meals." << std::endl;
    for (int i = 0; i < NUM_PHILOSOPHERS; ++i) {
        std::cout << "Philosopher " << i << " ate " << eat_counts[i] << " times." << std::endl;
    }

    return 0;
}