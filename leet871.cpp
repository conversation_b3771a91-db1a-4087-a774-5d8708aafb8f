#include <vector>
#include <queue>
using namespace std;
class Solution
{
public:
    int minRefuelStops(int target, int startFuel, vector<vector<int>> &stations)
    {
        stations.push_back({target, 0});
        int prev_pos = 0;
        int curr_fuel = startFuel;
        int ans = 0;
        priority_queue<int> pq;
        for (auto &station : stations)
        {
            curr_fuel -= station[0] - prev_pos;
            prev_pos = station[0];
            while (!pq.empty() && curr_fuel < 0)
            {
                curr_fuel += pq.top();
                pq.pop();
                ans++;
            }
            if (curr_fuel < 0)
            {
                return -1;
            }
            pq.push(station[1]);
        }
        return ans;
    }
};